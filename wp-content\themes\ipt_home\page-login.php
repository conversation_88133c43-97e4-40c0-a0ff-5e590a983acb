<?php
/**
 * Template Name: Login page template
 *
 * @package ipt_home
 */

get_header();
// Ensure toast.js is loaded
wp_enqueue_script('ipt-toast');
?>
<main class="w-full bg-white">
    <div class="max-sm:px-[16px] mt-[48px] md:mt-[60px] mx-auto w-full md:w-[630px] mb-[168px]">
        <h1 class="text-left text-24 md:text-32 font-bold text-black mb-[44px] font-semibold">Login</h1>
        <div class="shadow-none">
                <form class="space-y-6" id="loginForm">
                    <!-- Add the nonce field here -->
                    <?php wp_nonce_field('ipt_login_nonce', 'ipt_login_nonce'); ?>
                    
                    <div>
                        <label for="email" class="block text-14 font-medium text-gray-700">Email</label>
                        <div class="mt-1">
                            <input id="email" name="email" type="email" autocomplete="email" required 
                                class="!bg-ipt-bg-1 !h-[48px] appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none !text-neutral-strong !bg-neutral-subtle"
                                placeholder="<EMAIL>">
                            <p id="email-error" class="mt-1 text-red-500 text-sm hidden">Please enter your email</p>
                        </div>
                    </div>

                    <div>
                        <label for="password" class="block text-14 font-medium text-gray-700">Password</label>
                        <div class="mt-1 relative">
                            <input 
                                id="password" 
                                name="password" 
                                type="password" 
                                autocomplete="current-password" 
                                required 
                                class="!bg-ipt-bg-1 !h-[48px] appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none !text-neutral-strong !bg-neutral-subtle pr-10"
                                placeholder="••••••"
                            >
                            <a class="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer" onclick="togglePassword('password', this)">
                                <!-- Eye icon (shows when password is visible) - initially hidden -->
                                <svg class="h-5 w-5 text-gray-400 show-password hidden" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M15 12C15 13.6569 13.6569 15 12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6569 9 15 10.3431 15 12Z" stroke="#21272A" stroke-width="2"/>
                                    <path d="M6.34315 7.34315C4.84285 8.84344 4 11 4 12C4 13 4.84285 15.1566 6.34315 16.6569C7.84344 18.1571 9.87827 19 12 19C14.1217 19 16.1566 18.1571 17.6569 16.6569C19.1571 15.1566 20 13 20 12C20 11 19.1571 8.84344 17.6569 7.34315C16.1566 5.84285 14.1217 5 12 5C9.87827 5 7.84344 5.84285 6.34315 7.34315Z" stroke="#21272A" stroke-width="2"/>
                                </svg>
                                <!-- Eye with slash icon (shows when password is hidden) - initially visible -->
                                <svg class="h-5 w-5 text-gray-400 hide-password" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M3.99989 3L19.9999 21" stroke="#21272A" stroke-width="2" stroke-linecap="round"/>
                                    <path d="M14.8345 16.3775C13.9979 16.7803 13.0351 17 12 17C9.87827 17 7.84344 16.1571 6.34315 14.6569C4.84285 13.1566 4 11 4 10C4 9.29687 4.40519 8.10374 5.19371 7.00396" stroke="#21272A" stroke-width="2" stroke-linecap="round"/>
                                    <path d="M8.53113 6.04435C9.5888 5.36033 10.7843 5 12 5C14.1217 5 16.1566 5.84285 17.6569 7.34315C19.1571 8.84344 20 11 20 12C20 12.7031 19.5948 13.8963 18.8063 14.996" stroke="#21272A" stroke-width="2" stroke-linecap="round"/>
                                    <path d="M15 12C15 13.6569 13.6569 15 12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6569 9 15 10.3431 15 12Z" stroke="#21272A" stroke-width="2"/>
                                </svg>
                            </a>
                            <p id="password-error" class="mt-1 text-red-500 text-sm hidden">Please enter your password</p>
                        </div>
                    </div>

                    <div class="flex items-center justify-end">
                        <div class="text-sm">
                            <a href="<?php echo get_permalink(50); ?>" class="font-medium text-16 text-neutral-strong hover:text-neutral-strong active:text-neutral-strong focus:text-neutral-strong">Forgot password?</a>
                        </div>
                    </div>

                    <div>
                        <button type="button" id="loginFormBtn" class="w-full mx-auto flex justify-center py-2 px-4 text-primary-main hover:text-primary-main/80 rounded-md shadow-sm text-16 font-medium bg-brand-main hover:bg-brand-main/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-400">
                            Login
                        </button>
                        <!-- General error message -->
                        <p id="login-general-error" class="mt-3 text-center text-red-500 text-sm hidden"></p>
                    </div>

                    <div>
                        <button type="button" onclick="window.location.href='<?php echo get_permalink(48); ?>'" class="w-full mx-auto flex justify-center py-2 px-4 hover:text-white rounded-md shadow-sm text-16 font-medium text-white bg-neutral-strong hover:bg-neutral-strong/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-400">
                            No account? <span class="underline  pl-1">Sign up for free</span>
                        </button>
                        <div class="mt-6 text-center text-16 text-neutral-strong font-semibold">
                            By continuing, you agree to our 
                            <a href="<?php echo get_permalink(79); ?>" class="font-semibold text-neutral-strong hover:text-neutral-strong active:text-neutral-strong focus:text-neutral-strong underline">Privacy Policy</a>
                            and
                            <a href="<?php echo get_permalink(81); ?>" class="font-semibold text-neutral-strong hover:text-neutral-strong active:text-neutral-strong focus:text-neutral-strong underline">Terms of Service</a>
                        </div>
                    </div>
                </form>
                <div class="mt-6">
                   

                    <div class="mt-6 relative">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full md:w-[412px] mx-auto border-t border-gray-300"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-2 bg-white text-gray-500">Or</span>
                        </div>
                    </div>

                    <div class="mt-6 flex flex-col gap-[16px]">
                        <button type="button" onclick="handleGoogleLogin()" class="w-full md:w-[412px] mx-auto h-[56px] flex items-center py-[16px] px-[24px] border border-neutral-medium hover:border-brand-main rounded-md shadow-sm text-16 font-semibold text-neutral-strong bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-400">
                            <div class="flex items-center justify-center w-full">
                                <div class="w-[220px] flex items-center justify-start">
                                    <img class="h-5 w-5 mr-2" src="https://www.svgrepo.com/show/475656/google-color.svg" alt="Google logo">
                                    <span>Login with Google</span>
                                </div>
                            </div>
                        </button>
                        <button type="button" onclick="handleFacebookLogin()" class="w-full md:w-[412px] mx-auto h-[56px] flex items-center py-[16px] px-[24px] border border-neutral-medium hover:border-brand-main rounded-md shadow-sm text-16 font-semibold text-neutral-strong bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-400">
                            <div class="flex items-center justify-center w-full">
                                <div class="w-[220px] flex items-center justify-start">
                                    <img class="h-5 w-5 mr-2" src="<?php echo get_stylesheet_directory_uri(); ?>/assets/img/fb-ico.png" alt="Facebook logo">
                                    <span>Login with Facebook</span>
                                </div>
                            </div>
                        </button>
                        <button type="button" onclick='window.location.href="<?php echo get_permalink(48); ?>"' class="w-full md:w-[412px] mx-auto h-[56px] flex items-center py-[16px] px-[24px] border border-neutral-medium hover:border-brand-main rounded-md shadow-sm text-16 font-semibold text-neutral-strong bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-400">
                            <div class="flex items-center justify-center w-full">
                                <div class="w-[220px] flex items-center justify-start">
                                    <img class="h-5 w-5 mr-2" src="<?php echo get_stylesheet_directory_uri(); ?>/assets/img/email-ico.png" alt="Email logo">
                                    <span>Continue with Email</span>
                                </div>
                            </div>
                        </button>
                        <!-- <button type="button" onclick="handleAppleLogin()" class="w-full md:w-[412px] mx-auto h-[56px] flex items-center py-[16px] px-[24px] border border-neutral-medium hover:border-brand-main rounded-md shadow-sm text-16 font-semibold text-neutral-strong bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-400">
                            <div class="flex items-center justify-center w-full">
                                <div class="w-[220px] flex items-center justify-start">
                                    <img class="h-5 w-5 mr-2" src="<?php echo get_stylesheet_directory_uri(); ?>/assets/img/apple-ico.png" alt="Apple logo">
                                    <span>Sign in with Apple</span>
                                </div>
                            </div>
                        </button> -->
                        <!-- <div class="sso-widget-container items-center justify-center !outline-none ">      
                            <div id="saml_login_widget-2" class="widget widget_saml_login_widget sso-widget">			
                                <script>
                                    function submitSamlForm() {
                                        document.getElementById("miniorange-saml-sp-sso-login-form").submit();
                                    }
                                </script>
                                <form name="miniorange-saml-sp-sso-login-form" id="miniorange-saml-sp-sso-login-form" method="post" action="">
                                    <input type="hidden" name="option" value="saml_user_login">
                                    <font size="+1" style="vertical-align:top;"> </font>
                                    <a href="javascript:void(0);" onclick="submitSamlForm()" class="cursor-pointer w-full h-[56px] hover:text-brand-main/80 flex items-center justify-center py-[16px] px-[24px] !border-none text-16 font-semibold !text-brand-main hover:!text-brand-main bg-transparent shadow-none !outline-none focus:!outline-none active:!outline-none hover:!outline-none">Continue with SSO</a>
                                </form>
                            </div>
                        </div> -->
                    </div>
                </div>
         
        </div>
    </div>
    <!-- /. Page content -->
</main><!-- #page -->
<!-- Firebase auth handlers are loaded from external JS file: /assets/js/firebase-auth-handlers.js -->
<?php
get_footer(); 
?>
<script>
    jQuery(document).ready(function($) {
        // Function to show error for input fields
        function showError(fieldId, show, message = null) {
            const errorElement = document.getElementById(fieldId + '-error');
            if (errorElement) {
                if (show) {
                    if (message) {
                        errorElement.textContent = message;
                    }
                    errorElement.classList.remove('hidden');
                } else {
                    errorElement.classList.add('hidden');
                    // Restore default messages
                    if (fieldId === 'email') {
                        errorElement.textContent = 'Please enter your email';
                    } else if (fieldId === 'password') {
                        errorElement.textContent = 'Please enter your password';
                    }
                }
            }
        }
        
        // Function to show general error message
        function showGeneralError(show, message = null) {
            const errorElement = document.getElementById('login-general-error');
            if (errorElement) {
                if (show) {
                    if (message) {
                        errorElement.textContent = message;
                    }
                    errorElement.classList.remove('hidden');
                } else {
                    errorElement.classList.add('hidden');
                }
            }
        }
        
        // Function to validate email format
        function isValidEmail(email) {
            const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
            return emailRegex.test(email);
        }
        
        // Handle input changes to hide error messages
        $('#email, #password').on('input', function() {
            const fieldId = this.id;
            if (this.value.trim() !== '') {
                showError(fieldId, false);
                // Hide general error when user starts typing again
                showGeneralError(false);
            } else {
                // Show error if field is empty
                showError(fieldId, true);
            }
        });
        
        // Handle input blur (focus lost)
        $('#email, #password').on('blur', function() {
            const fieldId = this.id;
            const value = this.value.trim();
            
            if (value === '') {
                showError(fieldId, true);
            } else if (fieldId === 'email' && !isValidEmail(value)) {
                showError(fieldId, true, 'Please enter a valid email address');
            }
        });
        
        // Toggle password visibility function
        window.togglePassword = function(inputId, element) {
            const input = document.getElementById(inputId);
            const showIcon = element.querySelector('.show-password');
            const hideIcon = element.querySelector('.hide-password');

            if (input.type === 'password') {
                // Password is currently hidden, so show it
                input.type = 'text';
                // Show the "visible" icon (eye) since password is now visible
                showIcon.classList.remove('hidden');
                hideIcon.classList.add('hidden');
            } else {
                // Password is currently visible, so hide it
                input.type = 'password';
                // Show the "hidden" icon (eye with slash) since password is now hidden
                showIcon.classList.add('hidden');
                hideIcon.classList.remove('hidden');
            }
        };
        
        // Handle login button click
        $('#loginFormBtn').on('click', function(e) {
            // Hide general error
            showGeneralError(false);

            // Get form data
            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value.trim();

            // Validate and show errors
            let hasError = false;

            if (email === '') {
                showError('email', true);
                hasError = true;
            } else if (!isValidEmail(email)) {
                showError('email', true, 'Please enter a valid email address');
                hasError = true;
            } else {
                showError('email', false);
            }

            if (password === '') {
                showError('password', true);
                hasError = true;
            } else {
                showError('password', false);
            }

            if (hasError) {
                return;
            }

            // Show loading
            $('#loginFormBtn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Authenticating...');

            // First call GraphQL API for authentication
            const mutation = `
                mutation Auth_login($email: String!, $password: String!, $role_id: Int!) {
                    auth_login(
                        body: {
                            email: $email,
                            password: $password,
                            role_id: $role_id
                        }
                    ) {
                        id
                        info
                    }
                }
            `;

            const variables = {
                email: email,
                password: password,
                role_id: 2  // Customer role
            };


            $.ajax({
                url: iptHomeAjax.ajax_url,
                type: 'POST',
                dataType: 'json',
                data: {
                    action: 'ipt_home_graphql',
                    query: mutation,
                    variables: JSON.stringify(variables)
                },
                success: function(response) {

                    if (response.errors && response.errors.length > 0) {
                        showGeneralError(true, 'Invalid email or password.');
                        $('#loginFormBtn').prop('disabled', false).html('Login');
                    } else if (response.data && response.data.auth_login && response.data.auth_login.id) {
                        const customerId = response.data.auth_login.id;

                        // Now call WordPress login to save customer_id
                        performWordPressLogin(email, password, customerId, response.data.auth_login.info);
                    } else {
                        showGeneralError(true, 'Invalid email or password.');
                        $('#loginFormBtn').prop('disabled', false).html('Login');
                    }
                },
                error: function(xhr, status, error) {

                    showGeneralError(true, 'Invalid email or password.');
                    $('#loginFormBtn').prop('disabled', false).html('Login');
                }
            });
        });

        // Function to perform WordPress login with customer_id
        function performWordPressLogin(email, password, customerId, customerInfo = null) {


            $('#loginFormBtn').html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Logging in...');

            $.ajax({
                url: iptHomeAjax.ajax_url,
                type: 'POST',
                data: {
                    action: 'ipt_customer_login',
                    email: email,
                    password: password,
                    customer_id: customerId,
                    security: $('#ipt_login_nonce').val()
                },
                success: function(response) {


                    // Check response before accessing properties
                    if (response && typeof response === 'object') {
                        if (response.success) {
                            if(customerInfo && customerInfo != null) {
                                // Login successful - use the redirect URL from the response
                                window.location.href = response.data.redirect || '<?php echo home_url('customer/dashboard'); ?>';
                            } else {
                                localStorage.setItem('customerInfo', JSON.stringify(customerInfo));
                                window.location.href = response.data.redirect || '<?php echo home_url('get-started'); ?>';
                            }
                            
                        } else {
                            // Show error message
                            showGeneralError(true, 'Invalid email or password.');
                        }
                    } else {
                        // Handle non-object response

                        showGeneralError(true, 'Invalid email or password.');
                    }
                },
                error: function(xhr, status, error) {
                    // Handle Ajax error

                    showGeneralError(true, 'Invalid email or password.');
                },
                complete: function() {
                    // Restore login button
                    $('#loginFormBtn').prop('disabled', false).html('Login');
                }
            });
        }
        
        // Handle Enter key press in form
        $('#loginForm').on('keypress', function(e) {
            if (e.which === 13) { // Enter key
                e.preventDefault();
                $('#loginFormBtn').click();
            }
        });
    });
</script>
