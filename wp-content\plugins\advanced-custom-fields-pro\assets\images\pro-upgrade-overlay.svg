<svg width="1640" height="1640" viewBox="0 0 1640 1640" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.68" filter="url(#filter0_f_374_8088)">
<rect x="892.976" y="979.571" width="269.542" height="382.158" rx="100" transform="rotate(-45 892.976 979.571)" fill="#07E3BB"/>
</g>
<g opacity="0.68" filter="url(#filter1_f_374_8088)">
<rect x="1160" y="480" width="680" height="680" rx="340" transform="rotate(90 1160 480)" fill="url(#paint0_linear_374_8088)"/>
</g>
<defs>
<filter id="filter0_f_374_8088" x="634.398" y="530.398" width="977.979" height="977.979" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="150" result="effect1_foregroundBlur_374_8088"/>
</filter>
<filter id="filter1_f_374_8088" x="0" y="0" width="1640" height="1640" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="240" result="effect1_foregroundBlur_374_8088"/>
</filter>
<linearGradient id="paint0_linear_374_8088" x1="1160" y1="811.5" x2="1837.91" y2="807.485" gradientUnits="userSpaceOnUse">
<stop stop-color="#3E8BFF"/>
<stop offset="1" stop-color="#A45CFF"/>
</linearGradient>
</defs>
</svg>
