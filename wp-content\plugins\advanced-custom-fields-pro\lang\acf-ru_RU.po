# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-05-08T10:20:52+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: ru_RU\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: src/Site_Health/Site_Health.php:517
msgid "Number of Field Groups with Blocks and Other Locations"
msgstr ""
"Количество групп полей с правилами размещения как для блоков, так и для "
"других типов."

#: src/Site_Health/Site_Health.php:512
msgid "Number of Field Groups with Multiple Block Locations"
msgstr ""
"Количество групп полей, у которых несколько правил размещения для блоков."

#: src/Site_Health/Site_Health.php:507
msgid "Number of Field Groups with a Single Block Location"
msgstr ""
"Количество групп полей с единственным правилом размещения типа \"Блок\"."

#: src/Site_Health/Site_Health.php:476
msgid "All Location Rules"
msgstr "Все правила местонахождения"

#: includes/validation.php:144
msgid "Learn more"
msgstr "Подробнее"

#: includes/validation.php:133
msgid ""
"ACF was unable to perform validation because the provided nonce failed "
"verification."
msgstr ""
"ACF не смог выполнить проверку, потому что переданный nonce не прошёл "
"верификацию."

#: includes/validation.php:131
msgid ""
"ACF was unable to perform validation because no nonce was received by the "
"server."
msgstr "ACF не смог выполнить проверку, потому что сервер не получил nonce."

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:324
msgid "are developed and maintained by"
msgstr "разработано и обслуживается"

#: includes/admin/views/acf-post-type/advanced-settings.php:850
#: includes/admin/views/acf-taxonomy/advanced-settings.php:810
msgid "By default only admin users can edit this setting."
msgstr "По умолчанию этот параметр могут редактировать только администраторы."

#: includes/admin/views/acf-post-type/advanced-settings.php:848
#: includes/admin/views/acf-taxonomy/advanced-settings.php:808
msgid "By default only super admin users can edit this setting."
msgstr ""
"По умолчанию этот параметр могут редактировать только суперадминистраторы."

#: includes/admin/views/acf-field-group/field.php:322
msgid "Close and Add Field"
msgstr "Закрыть и добавить поле"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:804
msgid ""
"A PHP function name to be called to handle the content of a meta box on your "
"taxonomy. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""
"Имя PHP функции, которая будет вызвана для обработки содержимого мета-блока "
"в вашей таксономии. Для безопасности этот обратный вызов будет выполняться в "
"специальном контексте без доступа к каким-либо суперглобальным переменным, "
"таким как $_POST или $_GET."

#: includes/admin/views/acf-post-type/advanced-settings.php:842
msgid ""
"A PHP function name to be called when setting up the meta boxes for the edit "
"screen. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""
"Имя PHP-функции, которая будет вызвана при настройке мета-боксов для экрана "
"редактирования. Для безопасности этот обратный вызов будет выполняться в "
"специальном контексте без доступа к каким-либо суперглобальным переменным, "
"таким как $_POST или $_GET."

#: includes/fields/class-acf-field.php:359
msgid "Allow Access to Value in Editor UI"
msgstr "Разрешить доступ к значению в пользовательском интерфейсе редактора"

#: includes/fields/class-acf-field.php:341
msgid "Learn more."
msgstr "Узнать больше."

#. translators: %s A "Learn More" link to documentation explaining the setting
#. further.
#: includes/fields/class-acf-field.php:340
msgid ""
"Allow content editors to access and display the field value in the editor UI "
"using Block Bindings or the ACF Shortcode. %s"
msgstr ""
"Откройте доступ редакторам содержимого к отображению значения поля в "
"интерфейсе редактора с помощью привязки к блоку или шорткода ACF. %s"

#: src/Blocks/Bindings.php:67
msgid ""
"The requested ACF field type does not support output in Block Bindings or "
"the ACF shortcode."
msgstr ""

#: includes/api/api-template.php:1085 src/Blocks/Bindings.php:75
msgid ""
"The requested ACF field is not allowed to be output in bindings or the ACF "
"Shortcode."
msgstr ""
"Запрашиваемое поле ACF не разрешено для вывода в связке блока или шорткоде "
"ACF."

#: includes/api/api-template.php:1077
msgid ""
"The requested ACF field type does not support output in bindings or the ACF "
"Shortcode."
msgstr ""
"Запрашиваемый тип поля ACF не поддерживает вывод в связках или шорткоде ACF."

#: includes/api/api-template.php:1054
msgid "[The ACF shortcode cannot display fields from non-public posts]"
msgstr "[Шорткод ACF не может отображать поля из непубличных записей]"

#: includes/api/api-template.php:1011
msgid "[The ACF shortcode is disabled on this site]"
msgstr "[Шорткод ACF отключен на этом сайте]"

#: includes/fields/class-acf-field-icon_picker.php:451
msgid "Businessman Icon"
msgstr "Иконка Бизнесмен"

#: includes/fields/class-acf-field-icon_picker.php:443
msgid "Forums Icon"
msgstr "Иконка Форумы"

#: includes/fields/class-acf-field-icon_picker.php:722
msgid "YouTube Icon"
msgstr "Иконка YouTube"

#: includes/fields/class-acf-field-icon_picker.php:721
msgid "Yes (alt) Icon"
msgstr "Иконка Да (alt)"

#: includes/fields/class-acf-field-icon_picker.php:719
msgid "Xing Icon"
msgstr "Иконка Xing"

#: includes/fields/class-acf-field-icon_picker.php:718
msgid "WordPress (alt) Icon"
msgstr "Иконка WordPress (alt)"

#: includes/fields/class-acf-field-icon_picker.php:716
msgid "WhatsApp Icon"
msgstr "Иконка WhatsApp"

#: includes/fields/class-acf-field-icon_picker.php:715
msgid "Write Blog Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:714
msgid "Widgets Menus Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:713
msgid "View Site Icon"
msgstr "Иконка Показать сайт"

#: includes/fields/class-acf-field-icon_picker.php:712
msgid "Learn More Icon"
msgstr "Иконка Узнать больше"

#: includes/fields/class-acf-field-icon_picker.php:710
msgid "Add Page Icon"
msgstr "Иконка Добавить страницу"

#: includes/fields/class-acf-field-icon_picker.php:707
msgid "Video (alt3) Icon"
msgstr "Иконка Видео (alt3)"

#: includes/fields/class-acf-field-icon_picker.php:706
msgid "Video (alt2) Icon"
msgstr "Иконка Видео (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:705
msgid "Video (alt) Icon"
msgstr "Иконка Видео (alt)"

#: includes/fields/class-acf-field-icon_picker.php:702
msgid "Update (alt) Icon"
msgstr "Иконка Обновить (alt)"

#: includes/fields/class-acf-field-icon_picker.php:699
msgid "Universal Access (alt) Icon"
msgstr "Иконка Универсальный доступ (alt)"

#: includes/fields/class-acf-field-icon_picker.php:696
msgid "Twitter (alt) Icon"
msgstr "Иконка Twitter (alt)"

#: includes/fields/class-acf-field-icon_picker.php:694
msgid "Twitch Icon"
msgstr "Иконка Twitch"

#: includes/fields/class-acf-field-icon_picker.php:691
msgid "Tide Icon"
msgstr "Иконка Tide"

#: includes/fields/class-acf-field-icon_picker.php:690
msgid "Tickets (alt) Icon"
msgstr "Иконка Билеты (alt)"

#: includes/fields/class-acf-field-icon_picker.php:686
msgid "Text Page Icon"
msgstr "Иконка Текстовая страница"

#: includes/fields/class-acf-field-icon_picker.php:680
msgid "Table Row Delete Icon"
msgstr "Иконка Удалить строку таблицы"

#: includes/fields/class-acf-field-icon_picker.php:679
msgid "Table Row Before Icon"
msgstr "Иконка Перед строкой таблицы"

#: includes/fields/class-acf-field-icon_picker.php:678
msgid "Table Row After Icon"
msgstr "Иконка После строки таблицы"

#: includes/fields/class-acf-field-icon_picker.php:677
msgid "Table Col Delete Icon"
msgstr "Иконка Удалить колонку таблицы"

#: includes/fields/class-acf-field-icon_picker.php:676
msgid "Table Col Before Icon"
msgstr "Иконка Перед колонкой таблицы"

#: includes/fields/class-acf-field-icon_picker.php:675
msgid "Table Col After Icon"
msgstr "Иконка После колонки таблицы"

#: includes/fields/class-acf-field-icon_picker.php:674
msgid "Superhero (alt) Icon"
msgstr "Иконка Супергерой (alt)"

#: includes/fields/class-acf-field-icon_picker.php:673
msgid "Superhero Icon"
msgstr "Иконка Супергерой"

#: includes/fields/class-acf-field-icon_picker.php:667
msgid "Spotify Icon"
msgstr "Иконка Spotify"

#: includes/fields/class-acf-field-icon_picker.php:661
msgid "Shortcode Icon"
msgstr "Иконка Шорткод"

#: includes/fields/class-acf-field-icon_picker.php:660
msgid "Shield (alt) Icon"
msgstr "Иконка Щит (alt)"

#: includes/fields/class-acf-field-icon_picker.php:658
msgid "Share (alt2) Icon"
msgstr "Иконка Поделиться (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:657
msgid "Share (alt) Icon"
msgstr "Иконка Поделиться (alt)"

#: includes/fields/class-acf-field-icon_picker.php:652
msgid "Saved Icon"
msgstr "Иконка Сохранено"

#: includes/fields/class-acf-field-icon_picker.php:651
msgid "RSS Icon"
msgstr "Иконка RSS"

#: includes/fields/class-acf-field-icon_picker.php:650
msgid "REST API Icon"
msgstr "Иконка REST API"

#: includes/fields/class-acf-field-icon_picker.php:649
msgid "Remove Icon"
msgstr "Иконка Удалить"

#: includes/fields/class-acf-field-icon_picker.php:647
msgid "Reddit Icon"
msgstr "Ааываыва"

#: includes/fields/class-acf-field-icon_picker.php:644
msgid "Privacy Icon"
msgstr "Иконка Приватность"

#: includes/fields/class-acf-field-icon_picker.php:643
msgid "Printer Icon"
msgstr "Иконка Принтер"

#: includes/fields/class-acf-field-icon_picker.php:639
msgid "Podio Icon"
msgstr "Иконка Радио"

#: includes/fields/class-acf-field-icon_picker.php:638
msgid "Plus (alt2) Icon"
msgstr "Иконка Плюс (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:637
msgid "Plus (alt) Icon"
msgstr "Иконка Плюс (alt)"

#: includes/fields/class-acf-field-icon_picker.php:635
msgid "Plugins Checked Icon"
msgstr "Значок: Плагины проверены"

#: includes/fields/class-acf-field-icon_picker.php:632
msgid "Pinterest Icon"
msgstr "Иконка Pinterest"

#: includes/fields/class-acf-field-icon_picker.php:630
msgid "Pets Icon"
msgstr "Иконка Питомцы"

#: includes/fields/class-acf-field-icon_picker.php:628
msgid "PDF Icon"
msgstr "Иконка PDF"

#: includes/fields/class-acf-field-icon_picker.php:626
msgid "Palm Tree Icon"
msgstr "Иконка Пальмовое дерево"

#: includes/fields/class-acf-field-icon_picker.php:625
msgid "Open Folder Icon"
msgstr "Иконка Открыть папку"

#: includes/fields/class-acf-field-icon_picker.php:624
msgid "No (alt) Icon"
msgstr "Иконка Нет (alt)"

#: includes/fields/class-acf-field-icon_picker.php:619
msgid "Money (alt) Icon"
msgstr "Иконка Деньги (alt)"

#: includes/fields/class-acf-field-icon_picker.php:614
msgid "Menu (alt3) Icon"
msgstr "Иконка Меню (alt3)"

#: includes/fields/class-acf-field-icon_picker.php:613
msgid "Menu (alt2) Icon"
msgstr "Иконка Меню (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:612
msgid "Menu (alt) Icon"
msgstr "Иконка Меню (alt)"

#: includes/fields/class-acf-field-icon_picker.php:607
msgid "Spreadsheet Icon"
msgstr "Значок: Электронная таблица"

#: includes/fields/class-acf-field-icon_picker.php:606
msgid "Interactive Icon"
msgstr "Значок: Интерактивный"

#: includes/fields/class-acf-field-icon_picker.php:605
msgid "Document Icon"
msgstr "Иконка Документ"

#: includes/fields/class-acf-field-icon_picker.php:604
msgid "Default Icon"
msgstr "Иконка По умолчанию"

#: includes/fields/class-acf-field-icon_picker.php:598
msgid "Location (alt) Icon"
msgstr "Иконка Местоположение (alt)"

#: includes/fields/class-acf-field-icon_picker.php:595
msgid "LinkedIn Icon"
msgstr "Иконка LinkedIn"

#: includes/fields/class-acf-field-icon_picker.php:590
msgid "Instagram Icon"
msgstr "Иконка Instagram"

#: includes/fields/class-acf-field-icon_picker.php:589
msgid "Insert Before Icon"
msgstr "Иконка Вставить перед"

#: includes/fields/class-acf-field-icon_picker.php:588
msgid "Insert After Icon"
msgstr "Иконка Вставить после"

#: includes/fields/class-acf-field-icon_picker.php:587
msgid "Insert Icon"
msgstr "Иконка Вставить"

#: includes/fields/class-acf-field-icon_picker.php:586
msgid "Info Outline Icon"
msgstr "Значок: Инфо (обведен)"

#: includes/fields/class-acf-field-icon_picker.php:583
msgid "Images (alt2) Icon"
msgstr "Иконка Изображения (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:582
msgid "Images (alt) Icon"
msgstr "Иконка Изображения (alt)"

#: includes/fields/class-acf-field-icon_picker.php:581
msgid "Rotate Right Icon"
msgstr "Иконка Повернуть вправо"

#: includes/fields/class-acf-field-icon_picker.php:580
msgid "Rotate Left Icon"
msgstr "Иконка Повернуть влево"

#: includes/fields/class-acf-field-icon_picker.php:579
msgid "Rotate Icon"
msgstr "Иконка Повернуть"

#: includes/fields/class-acf-field-icon_picker.php:578
msgid "Flip Vertical Icon"
msgstr "Иконка Отразить по вертикали"

#: includes/fields/class-acf-field-icon_picker.php:577
msgid "Flip Horizontal Icon"
msgstr "Иконка Отразить по горизонтали"

#: includes/fields/class-acf-field-icon_picker.php:575
msgid "Crop Icon"
msgstr "Иконка Обрезать"

#: includes/fields/class-acf-field-icon_picker.php:574
msgid "ID (alt) Icon"
msgstr "Иконка ID (alt)"

#: includes/fields/class-acf-field-icon_picker.php:572
msgid "HTML Icon"
msgstr "Иконка HTML"

#: includes/fields/class-acf-field-icon_picker.php:571
msgid "Hourglass Icon"
msgstr "Значок: песочные часы"

#: includes/fields/class-acf-field-icon_picker.php:568
msgid "Heading Icon"
msgstr "Значок заголовка"

#: includes/fields/class-acf-field-icon_picker.php:564
msgid "Google Icon"
msgstr "Иконка Google"

#: includes/fields/class-acf-field-icon_picker.php:563
msgid "Games Icon"
msgstr "Иконка Игры"

#: includes/fields/class-acf-field-icon_picker.php:562
msgid "Fullscreen Exit (alt) Icon"
msgstr "Иконка Выйти из полноэкранного режима"

#: includes/fields/class-acf-field-icon_picker.php:561
msgid "Fullscreen (alt) Icon"
msgstr "Иконка Полноэкранный режим (alt)"

#: includes/fields/class-acf-field-icon_picker.php:558
msgid "Status Icon"
msgstr "Иконка Статус"

#: includes/fields/class-acf-field-icon_picker.php:556
msgid "Image Icon"
msgstr "Иконка Изображение"

#: includes/fields/class-acf-field-icon_picker.php:555
msgid "Gallery Icon"
msgstr "Иконка Галерея"

#: includes/fields/class-acf-field-icon_picker.php:554
msgid "Chat Icon"
msgstr "Иконка Чат"

#: includes/fields/class-acf-field-icon_picker.php:553
#: includes/fields/class-acf-field-icon_picker.php:602
msgid "Audio Icon"
msgstr "Иконка Аудио"

#: includes/fields/class-acf-field-icon_picker.php:552
msgid "Aside Icon"
msgstr "Иконка Сайдбар"

#: includes/fields/class-acf-field-icon_picker.php:551
msgid "Food Icon"
msgstr "Иконка Еда"

#: includes/fields/class-acf-field-icon_picker.php:544
msgid "Exit Icon"
msgstr "Иконка Выйти"

#: includes/fields/class-acf-field-icon_picker.php:543
msgid "Excerpt View Icon"
msgstr "Иконка Отрывок"

#: includes/fields/class-acf-field-icon_picker.php:542
msgid "Embed Video Icon"
msgstr "Иконка Встроенное видео"

#: includes/fields/class-acf-field-icon_picker.php:541
msgid "Embed Post Icon"
msgstr "Иконка Встроенная запись"

#: includes/fields/class-acf-field-icon_picker.php:540
msgid "Embed Photo Icon"
msgstr "Иконка Встроенное фото"

#: includes/fields/class-acf-field-icon_picker.php:539
msgid "Embed Generic Icon"
msgstr "Значок: Встроеное общее содержимое"

#: includes/fields/class-acf-field-icon_picker.php:538
msgid "Embed Audio Icon"
msgstr "Иконка Встроенное аудио"

#: includes/fields/class-acf-field-icon_picker.php:537
msgid "Email (alt2) Icon"
msgstr "Иконка Email (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:534
msgid "Ellipsis Icon"
msgstr "Значок: Многоточие"

#: includes/fields/class-acf-field-icon_picker.php:530
msgid "Unordered List Icon"
msgstr "Значок: Неупорядоченный список"

#: includes/fields/class-acf-field-icon_picker.php:525
msgid "RTL Icon"
msgstr "Значок: RTL"

#: includes/fields/class-acf-field-icon_picker.php:518
msgid "Ordered List RTL Icon"
msgstr "Значок: Упорядоченный список RTL"

#: includes/fields/class-acf-field-icon_picker.php:517
msgid "Ordered List Icon"
msgstr "Значок: Сортированный список"

#: includes/fields/class-acf-field-icon_picker.php:516
msgid "LTR Icon"
msgstr "Значок: LTR"

#: includes/fields/class-acf-field-icon_picker.php:508
msgid "Custom Character Icon"
msgstr "Значок: Спецсимвол"

#: includes/fields/class-acf-field-icon_picker.php:500
msgid "Edit Page Icon"
msgstr "Иконка Редактировать страницу"

#: includes/fields/class-acf-field-icon_picker.php:499
msgid "Edit Large Icon"
msgstr "Значок: Редактировать (большой)"

#: includes/fields/class-acf-field-icon_picker.php:497
msgid "Drumstick Icon"
msgstr "Иконка Барабанная палочка"

#: includes/fields/class-acf-field-icon_picker.php:493
msgid "Database View Icon"
msgstr "Иконка Отобразить базу данных"

#: includes/fields/class-acf-field-icon_picker.php:492
msgid "Database Remove Icon"
msgstr "Иконка Удалить базу данных"

#: includes/fields/class-acf-field-icon_picker.php:491
msgid "Database Import Icon"
msgstr "Иконка Импорт базы данных"

#: includes/fields/class-acf-field-icon_picker.php:490
msgid "Database Export Icon"
msgstr "Иконка Экспорт базы данных"

#: includes/fields/class-acf-field-icon_picker.php:489
msgid "Database Add Icon"
msgstr "Иконка Добавить базу данных"

#: includes/fields/class-acf-field-icon_picker.php:488
msgid "Database Icon"
msgstr "Иконка База данных"

#: includes/fields/class-acf-field-icon_picker.php:486
msgid "Cover Image Icon"
msgstr "Значок: Изображение-обложка"

#: includes/fields/class-acf-field-icon_picker.php:485
msgid "Volume On Icon"
msgstr "Иконка Включить звук"

#: includes/fields/class-acf-field-icon_picker.php:484
msgid "Volume Off Icon"
msgstr "Иконка Выключить звук"

#: includes/fields/class-acf-field-icon_picker.php:483
msgid "Skip Forward Icon"
msgstr "Значок: Перемотать вперёд"

#: includes/fields/class-acf-field-icon_picker.php:482
msgid "Skip Back Icon"
msgstr "Значок: Перемотать назад"

#: includes/fields/class-acf-field-icon_picker.php:481
msgid "Repeat Icon"
msgstr "Иконка Повторить"

#: includes/fields/class-acf-field-icon_picker.php:480
msgid "Play Icon"
msgstr "Иконка Воспроизвести"

#: includes/fields/class-acf-field-icon_picker.php:479
msgid "Pause Icon"
msgstr "Иконка Пауза"

#: includes/fields/class-acf-field-icon_picker.php:478
msgid "Forward Icon"
msgstr "Иконка Вперед"

#: includes/fields/class-acf-field-icon_picker.php:477
msgid "Back Icon"
msgstr "Иконка Назад"

#: includes/fields/class-acf-field-icon_picker.php:476
msgid "Columns Icon"
msgstr "Иконка Колонки"

#: includes/fields/class-acf-field-icon_picker.php:475
msgid "Color Picker Icon"
msgstr "Иконка Подборщик цвета"

#: includes/fields/class-acf-field-icon_picker.php:474
msgid "Coffee Icon"
msgstr "Иконка Кофе"

#: includes/fields/class-acf-field-icon_picker.php:473
msgid "Code Standards Icon"
msgstr "Иконка Стандарты кода"

#: includes/fields/class-acf-field-icon_picker.php:472
msgid "Cloud Upload Icon"
msgstr "Иконка Загрузить в облако"

#: includes/fields/class-acf-field-icon_picker.php:471
msgid "Cloud Saved Icon"
msgstr "Иконка Сохранено в облаке"

#: includes/fields/class-acf-field-icon_picker.php:460
msgid "Car Icon"
msgstr "Иконка Машина"

#: includes/fields/class-acf-field-icon_picker.php:459
msgid "Camera (alt) Icon"
msgstr "Иконка Камера (alt)"

#: includes/fields/class-acf-field-icon_picker.php:455
msgid "Calculator Icon"
msgstr "Иконка Калькулятор"

#: includes/fields/class-acf-field-icon_picker.php:454
msgid "Button Icon"
msgstr "Иконка Кнопка"

#: includes/fields/class-acf-field-icon_picker.php:452
msgid "Businessperson Icon"
msgstr "Значок: Бизнесмен"

#: includes/fields/class-acf-field-icon_picker.php:449
msgid "Tracking Icon"
msgstr "Значок: Отслеживание"

#: includes/fields/class-acf-field-icon_picker.php:448
msgid "Topics Icon"
msgstr "Иконка Темы"

#: includes/fields/class-acf-field-icon_picker.php:447
msgid "Replies Icon"
msgstr "Значок: Ответы"

#: includes/fields/class-acf-field-icon_picker.php:446
msgid "PM Icon"
msgstr "Иконка PM"

#: includes/fields/class-acf-field-icon_picker.php:444
msgid "Friends Icon"
msgstr "Иконка Друзья"

#: includes/fields/class-acf-field-icon_picker.php:442
msgid "Community Icon"
msgstr "Иконка Сообщество"

#: includes/fields/class-acf-field-icon_picker.php:441
msgid "BuddyPress Icon"
msgstr "Иконка BuddyPress"

#: includes/fields/class-acf-field-icon_picker.php:440
msgid "bbPress Icon"
msgstr "Иконка bbPress"

#: includes/fields/class-acf-field-icon_picker.php:439
msgid "Activity Icon"
msgstr "Иконка Активность"

#: includes/fields/class-acf-field-icon_picker.php:438
msgid "Book (alt) Icon"
msgstr "Иконка Книга (alt)"

#: includes/fields/class-acf-field-icon_picker.php:436
msgid "Block Default Icon"
msgstr "Значок: Блок по умолчанию"

#: includes/fields/class-acf-field-icon_picker.php:435
msgid "Bell Icon"
msgstr "Иконка Колокол"

#: includes/fields/class-acf-field-icon_picker.php:434
msgid "Beer Icon"
msgstr "Иконка Пиво"

#: includes/fields/class-acf-field-icon_picker.php:433
msgid "Bank Icon"
msgstr "Иконка Банк"

#: includes/fields/class-acf-field-icon_picker.php:429
msgid "Arrow Up (alt2) Icon"
msgstr "Значок: Стрелка вверх (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:428
msgid "Arrow Up (alt) Icon"
msgstr "Значок: Стрелка вверх (alt)"

#: includes/fields/class-acf-field-icon_picker.php:426
msgid "Arrow Right (alt2) Icon"
msgstr "Значок: Стрелка вправо (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:425
msgid "Arrow Right (alt) Icon"
msgstr "Значок: Стрелка вправо (alt)"

#: includes/fields/class-acf-field-icon_picker.php:423
msgid "Arrow Left (alt2) Icon"
msgstr "Значок: Стрелка влево (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:422
msgid "Arrow Left (alt) Icon"
msgstr "Значок: Стрелка влево (alt)"

#: includes/fields/class-acf-field-icon_picker.php:420
msgid "Arrow Down (alt2) Icon"
msgstr "Значок: Стрелка вниз (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:419
msgid "Arrow Down (alt) Icon"
msgstr "Значок: Стрелка вниз (alt)"

#: includes/fields/class-acf-field-icon_picker.php:415
msgid "Amazon Icon"
msgstr "Иконка Amazon"

#: includes/fields/class-acf-field-icon_picker.php:414
msgid "Align Wide Icon"
msgstr "Значок: Выравнивание по ширине"

#: includes/fields/class-acf-field-icon_picker.php:412
msgid "Align Pull Right Icon"
msgstr "Значок: Выравнивание по правому краю"

#: includes/fields/class-acf-field-icon_picker.php:411
msgid "Align Pull Left Icon"
msgstr "Значок: Выравнивание по левому краю"

#: includes/fields/class-acf-field-icon_picker.php:408
msgid "Align Full Width Icon"
msgstr "Значок: Выравнивание по всей ширине"

#: includes/fields/class-acf-field-icon_picker.php:405
msgid "Airplane Icon"
msgstr "Иконка Самолет"

#: includes/fields/class-acf-field-icon_picker.php:402
msgid "Site (alt3) Icon"
msgstr "Иконка Сайт (alt3)"

#: includes/fields/class-acf-field-icon_picker.php:401
msgid "Site (alt2) Icon"
msgstr "Иконка Сайт (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:400
msgid "Site (alt) Icon"
msgstr "Иконка Сайт (alt)"

#: includes/admin/views/options-page-preview.php:26
msgid "Upgrade to ACF PRO to create options pages in just a few clicks"
msgstr "Обновитесь до ACF PRO чтобы создавать страницы настроек в пару кликов"

#: includes/ajax/class-acf-ajax-query-users.php:24
msgid "Invalid request args."
msgstr "Неверные аргументы запроса."

#: includes/ajax/class-acf-ajax-check-screen.php:37
#: includes/ajax/class-acf-ajax-local-json-diff.php:37
#: includes/ajax/class-acf-ajax-query-users.php:33
#: includes/ajax/class-acf-ajax-upgrade.php:24
#: includes/ajax/class-acf-ajax-user-setting.php:38
msgid "Sorry, you do not have permission to do that."
msgstr "Извините, у вас нет разрешения чтобы делать это."

#: src/Site_Health/Site_Health.php:715
msgid "Blocks Using Post Meta"
msgstr "Блоки, использующие мета данные записей"

#: includes/admin/views/acf-field-group/pro-features.php:25
#: includes/admin/views/acf-field-group/pro-features.php:27
#: includes/admin/views/global/header.php:27
msgid "ACF PRO logo"
msgstr "Логотип ACF PRO"

#: includes/admin/views/acf-field-group/field.php:37
msgid "ACF PRO Logo"
msgstr "Логотип ACF PRO"

#. translators: %s - field/param name
#: includes/fields/class-acf-field-icon_picker.php:788
msgid "%s requires a valid attachment ID when type is set to media_library."
msgstr "%s требует валидный ID вложения когда установлен тип media_library."

#. translators: %s - field name
#: includes/fields/class-acf-field-icon_picker.php:772
msgid "%s is a required property of acf."
msgstr "%s является обязательным свойством acf."

#: includes/fields/class-acf-field-icon_picker.php:748
msgid "The value of icon to save."
msgstr "Значение иконки для сохранения."

#: includes/fields/class-acf-field-icon_picker.php:742
msgid "The type of icon to save."
msgstr "Тип иконки для сохранения."

#: includes/fields/class-acf-field-icon_picker.php:720
msgid "Yes Icon"
msgstr "Иконка Да"

#: includes/fields/class-acf-field-icon_picker.php:717
msgid "WordPress Icon"
msgstr "Иконка WordPress"

#: includes/fields/class-acf-field-icon_picker.php:709
msgid "Warning Icon"
msgstr "Иконка Предупреждение"

#: includes/fields/class-acf-field-icon_picker.php:708
msgid "Visibility Icon"
msgstr "Иконка Видимость"

#: includes/fields/class-acf-field-icon_picker.php:704
msgid "Vault Icon"
msgstr "Иконка Хранилище"

#: includes/fields/class-acf-field-icon_picker.php:703
msgid "Upload Icon"
msgstr "Иконка Загрузить"

#: includes/fields/class-acf-field-icon_picker.php:701
msgid "Update Icon"
msgstr "Иконка Обновить"

#: includes/fields/class-acf-field-icon_picker.php:700
msgid "Unlock Icon"
msgstr "Иконка Разблокировать"

#: includes/fields/class-acf-field-icon_picker.php:698
msgid "Universal Access Icon"
msgstr "Иконка Универсальный доступ"

#: includes/fields/class-acf-field-icon_picker.php:697
msgid "Undo Icon"
msgstr "Иконка Отменить"

#: includes/fields/class-acf-field-icon_picker.php:695
msgid "Twitter Icon"
msgstr "Иконка Twitter"

#: includes/fields/class-acf-field-icon_picker.php:693
msgid "Trash Icon"
msgstr "Иконка Мусорка"

#: includes/fields/class-acf-field-icon_picker.php:692
msgid "Translation Icon"
msgstr "Иконка Перевод"

#: includes/fields/class-acf-field-icon_picker.php:689
msgid "Tickets Icon"
msgstr "Иконка Билеты"

#: includes/fields/class-acf-field-icon_picker.php:688
msgid "Thumbs Up Icon"
msgstr "Значок: Палец вверх"

#: includes/fields/class-acf-field-icon_picker.php:687
msgid "Thumbs Down Icon"
msgstr "Значок: Палец вниз"

#: includes/fields/class-acf-field-icon_picker.php:608
#: includes/fields/class-acf-field-icon_picker.php:685
msgid "Text Icon"
msgstr "Иконка Текст"

#: includes/fields/class-acf-field-icon_picker.php:684
msgid "Testimonial Icon"
msgstr "Значок: Отзыв"

#: includes/fields/class-acf-field-icon_picker.php:683
msgid "Tagcloud Icon"
msgstr "Иконка Облако меток"

#: includes/fields/class-acf-field-icon_picker.php:682
msgid "Tag Icon"
msgstr "Иконка Метка"

#: includes/fields/class-acf-field-icon_picker.php:681
msgid "Tablet Icon"
msgstr "Иконка Планшет"

#: includes/fields/class-acf-field-icon_picker.php:672
msgid "Store Icon"
msgstr "Иконка Магазин"

#: includes/fields/class-acf-field-icon_picker.php:671
msgid "Sticky Icon"
msgstr "Значок: Закрепленный"

#: includes/fields/class-acf-field-icon_picker.php:670
msgid "Star Half Icon"
msgstr "Иконка Звезда половина"

#: includes/fields/class-acf-field-icon_picker.php:669
msgid "Star Filled Icon"
msgstr "Иконка Звезда заполненная"

#: includes/fields/class-acf-field-icon_picker.php:668
msgid "Star Empty Icon"
msgstr "Иконка Звезда пустая"

#: includes/fields/class-acf-field-icon_picker.php:666
msgid "Sos Icon"
msgstr "Значок: SOS"

#: includes/fields/class-acf-field-icon_picker.php:665
msgid "Sort Icon"
msgstr "Иконка Сортировать"

#: includes/fields/class-acf-field-icon_picker.php:664
msgid "Smiley Icon"
msgstr "Значок: Смайлик"

#: includes/fields/class-acf-field-icon_picker.php:663
msgid "Smartphone Icon"
msgstr "Иконка Смартфон"

#: includes/fields/class-acf-field-icon_picker.php:662
msgid "Slides Icon"
msgstr "Иконка Слайды"

#: includes/fields/class-acf-field-icon_picker.php:659
msgid "Shield Icon"
msgstr "Иконка Щит"

#: includes/fields/class-acf-field-icon_picker.php:656
msgid "Share Icon"
msgstr "Иконка Поделиться"

#: includes/fields/class-acf-field-icon_picker.php:655
msgid "Search Icon"
msgstr "Иконка Поиск"

#: includes/fields/class-acf-field-icon_picker.php:654
msgid "Screen Options Icon"
msgstr "Значок: Настройки экрана"

#: includes/fields/class-acf-field-icon_picker.php:653
msgid "Schedule Icon"
msgstr "Иконка Расписание"

#: includes/fields/class-acf-field-icon_picker.php:648
msgid "Redo Icon"
msgstr "Иконка Повторить"

#: includes/fields/class-acf-field-icon_picker.php:646
msgid "Randomize Icon"
msgstr "Значок: Перемешать"

#: includes/fields/class-acf-field-icon_picker.php:645
msgid "Products Icon"
msgstr "Иконка Товары"

#: includes/fields/class-acf-field-icon_picker.php:642
msgid "Pressthis Icon"
msgstr "Значок: Опубликовать"

#: includes/fields/class-acf-field-icon_picker.php:641
msgid "Post Status Icon"
msgstr "Иконка Статус записи"

#: includes/fields/class-acf-field-icon_picker.php:640
msgid "Portfolio Icon"
msgstr "Иконка Портфолио"

#: includes/fields/class-acf-field-icon_picker.php:636
msgid "Plus Icon"
msgstr "Иконка Плюс"

#: includes/fields/class-acf-field-icon_picker.php:634
msgid "Playlist Video Icon"
msgstr "Иконка Видео плейлист"

#: includes/fields/class-acf-field-icon_picker.php:633
msgid "Playlist Audio Icon"
msgstr "Иконка Аудио плейлист"

#: includes/fields/class-acf-field-icon_picker.php:631
msgid "Phone Icon"
msgstr "Иконка Телефон"

#: includes/fields/class-acf-field-icon_picker.php:629
msgid "Performance Icon"
msgstr "Значок: Производительность"

#: includes/fields/class-acf-field-icon_picker.php:627
msgid "Paperclip Icon"
msgstr "Значок: Скрепка"

#: includes/fields/class-acf-field-icon_picker.php:623
msgid "No Icon"
msgstr "Иконка Нет"

#: includes/fields/class-acf-field-icon_picker.php:622
msgid "Networking Icon"
msgstr "Значок: Сеть"

#: includes/fields/class-acf-field-icon_picker.php:621
msgid "Nametag Icon"
msgstr "Значок: Бейдж"

#: includes/fields/class-acf-field-icon_picker.php:620
msgid "Move Icon"
msgstr "Иконка Переместить"

#: includes/fields/class-acf-field-icon_picker.php:618
msgid "Money Icon"
msgstr "Иконка Деньги"

#: includes/fields/class-acf-field-icon_picker.php:617
msgid "Minus Icon"
msgstr "Иконка Минус"

#: includes/fields/class-acf-field-icon_picker.php:616
msgid "Migrate Icon"
msgstr "Иконка Перенос"

#: includes/fields/class-acf-field-icon_picker.php:615
msgid "Microphone Icon"
msgstr "Иконка Микрофон"

#: includes/fields/class-acf-field-icon_picker.php:610
msgid "Megaphone Icon"
msgstr "Иконка Мегафон"

#: includes/fields/class-acf-field-icon_picker.php:600
msgid "Marker Icon"
msgstr "Иконка Маркер"

#: includes/fields/class-acf-field-icon_picker.php:599
msgid "Lock Icon"
msgstr "Иконка Закрыть"

#: includes/fields/class-acf-field-icon_picker.php:597
msgid "Location Icon"
msgstr "Иконка Местоположение"

#: includes/fields/class-acf-field-icon_picker.php:596
msgid "List View Icon"
msgstr "Значок: Просмотр списка"

#: includes/fields/class-acf-field-icon_picker.php:594
msgid "Lightbulb Icon"
msgstr "Иконка Лампочка"

#: includes/fields/class-acf-field-icon_picker.php:593
msgid "Left Right Icon"
msgstr "Значок: Слева направо"

#: includes/fields/class-acf-field-icon_picker.php:592
msgid "Layout Icon"
msgstr "Значок: Макет"

#: includes/fields/class-acf-field-icon_picker.php:591
msgid "Laptop Icon"
msgstr "Иконка Ноутбук"

#: includes/fields/class-acf-field-icon_picker.php:585
msgid "Info Icon"
msgstr "Иконка Инфо"

#: includes/fields/class-acf-field-icon_picker.php:584
msgid "Index Card Icon"
msgstr "Значок: Картотека"

#: includes/fields/class-acf-field-icon_picker.php:573
msgid "ID Icon"
msgstr "Иконка ID"

#: includes/fields/class-acf-field-icon_picker.php:570
msgid "Hidden Icon"
msgstr "Значок: Скрытый"

#: includes/fields/class-acf-field-icon_picker.php:569
msgid "Heart Icon"
msgstr "Иконка Сердце"

#: includes/fields/class-acf-field-icon_picker.php:567
msgid "Hammer Icon"
msgstr "Иконка Молот"

#: includes/fields/class-acf-field-icon_picker.php:445
#: includes/fields/class-acf-field-icon_picker.php:566
msgid "Groups Icon"
msgstr "Иконка Группы"

#: includes/fields/class-acf-field-icon_picker.php:565
msgid "Grid View Icon"
msgstr "Значок: Просмотр сеткой"

#: includes/fields/class-acf-field-icon_picker.php:560
msgid "Forms Icon"
msgstr "Иконка Формы"

#: includes/fields/class-acf-field-icon_picker.php:550
msgid "Flag Icon"
msgstr "Иконка Флаг"

#: includes/fields/class-acf-field-icon_picker.php:549
#: includes/fields/class-acf-field-icon_picker.php:576
msgid "Filter Icon"
msgstr "Иконка Фильтр"

#: includes/fields/class-acf-field-icon_picker.php:548
msgid "Feedback Icon"
msgstr "Иконка Обратная связь"

#: includes/fields/class-acf-field-icon_picker.php:547
msgid "Facebook (alt) Icon"
msgstr "Иконка Facebook (alt)"

#: includes/fields/class-acf-field-icon_picker.php:546
msgid "Facebook Icon"
msgstr "Иконка Facebook"

#: includes/fields/class-acf-field-icon_picker.php:545
msgid "External Icon"
msgstr "Значок: Внешний"

#: includes/fields/class-acf-field-icon_picker.php:536
msgid "Email (alt) Icon"
msgstr "Иконка Email (alt)"

#: includes/fields/class-acf-field-icon_picker.php:535
msgid "Email Icon"
msgstr "Иконка Email"

#: includes/fields/class-acf-field-icon_picker.php:533
#: includes/fields/class-acf-field-icon_picker.php:559
#: includes/fields/class-acf-field-icon_picker.php:609
msgid "Video Icon"
msgstr "Иконка Видео"

#: includes/fields/class-acf-field-icon_picker.php:532
msgid "Unlink Icon"
msgstr "Значок: Отвязать ссылку"

#: includes/fields/class-acf-field-icon_picker.php:531
msgid "Underline Icon"
msgstr "Значок: Подчеркнутый"

#: includes/fields/class-acf-field-icon_picker.php:529
msgid "Text Color Icon"
msgstr "Значок: Цвет текста"

#: includes/fields/class-acf-field-icon_picker.php:528
msgid "Table Icon"
msgstr "Иконка Таблица"

#: includes/fields/class-acf-field-icon_picker.php:527
msgid "Strikethrough Icon"
msgstr "Значок: Зачеркнуто"

#: includes/fields/class-acf-field-icon_picker.php:526
msgid "Spellcheck Icon"
msgstr "Значок: Проверка правописания"

#: includes/fields/class-acf-field-icon_picker.php:524
msgid "Remove Formatting Icon"
msgstr "Значок: Удалить форматирование"

#: includes/fields/class-acf-field-icon_picker.php:523
#: includes/fields/class-acf-field-icon_picker.php:557
msgid "Quote Icon"
msgstr "Иконка Цитата"

#: includes/fields/class-acf-field-icon_picker.php:522
msgid "Paste Word Icon"
msgstr "Иконка Вставить слово"

#: includes/fields/class-acf-field-icon_picker.php:521
msgid "Paste Text Icon"
msgstr "Иконка Вставить текст"

#: includes/fields/class-acf-field-icon_picker.php:520
msgid "Paragraph Icon"
msgstr "Иконка Параграф"

#: includes/fields/class-acf-field-icon_picker.php:519
msgid "Outdent Icon"
msgstr "Значок: Отступ слева"

#: includes/fields/class-acf-field-icon_picker.php:515
msgid "Kitchen Sink Icon"
msgstr "Значок: Кухонная раковина"

#: includes/fields/class-acf-field-icon_picker.php:514
msgid "Justify Icon"
msgstr "Значок: Выравнивание"

#: includes/fields/class-acf-field-icon_picker.php:513
msgid "Italic Icon"
msgstr "Иконка Курсив"

#: includes/fields/class-acf-field-icon_picker.php:512
msgid "Insert More Icon"
msgstr "Значок: Вставить больше"

#: includes/fields/class-acf-field-icon_picker.php:511
msgid "Indent Icon"
msgstr "Значок: Отступ справа"

#: includes/fields/class-acf-field-icon_picker.php:510
msgid "Help Icon"
msgstr "Иконка Помощь"

#: includes/fields/class-acf-field-icon_picker.php:509
msgid "Expand Icon"
msgstr "Иконка Развернуть"

#: includes/fields/class-acf-field-icon_picker.php:507
msgid "Contract Icon"
msgstr "Значок: Сжать"

#: includes/fields/class-acf-field-icon_picker.php:506
#: includes/fields/class-acf-field-icon_picker.php:603
msgid "Code Icon"
msgstr "Иконка Код"

#: includes/fields/class-acf-field-icon_picker.php:505
msgid "Break Icon"
msgstr "Значок: Разбить"

#: includes/fields/class-acf-field-icon_picker.php:504
msgid "Bold Icon"
msgstr "Иконка Жирный"

#: includes/fields/class-acf-field-icon_picker.php:498
msgid "Edit Icon"
msgstr "Иконка Редактировать"

#: includes/fields/class-acf-field-icon_picker.php:496
msgid "Download Icon"
msgstr "Значок: Скачать"

#: includes/fields/class-acf-field-icon_picker.php:495
msgid "Dismiss Icon"
msgstr "Значок: отклонить"

#: includes/fields/class-acf-field-icon_picker.php:494
msgid "Desktop Icon"
msgstr "Значок: рабочий стол"

#: includes/fields/class-acf-field-icon_picker.php:487
msgid "Dashboard Icon"
msgstr "Значок: консоль"

#: includes/fields/class-acf-field-icon_picker.php:470
msgid "Cloud Icon"
msgstr "Иконка Облако"

#: includes/fields/class-acf-field-icon_picker.php:469
msgid "Clock Icon"
msgstr "Значок: Часы"

#: includes/fields/class-acf-field-icon_picker.php:468
msgid "Clipboard Icon"
msgstr "Значок: буфер обмена"

#: includes/fields/class-acf-field-icon_picker.php:467
msgid "Chart Pie Icon"
msgstr "Значок: Круговая диаграмма"

#: includes/fields/class-acf-field-icon_picker.php:466
msgid "Chart Line Icon"
msgstr "Значок: Линейный график"

#: includes/fields/class-acf-field-icon_picker.php:465
msgid "Chart Bar Icon"
msgstr "Значок: Гистограмма"

#: includes/fields/class-acf-field-icon_picker.php:464
msgid "Chart Area Icon"
msgstr "Значок: Диаграмма площади"

#: includes/fields/class-acf-field-icon_picker.php:463
msgid "Category Icon"
msgstr "Значок: рубрика"

#: includes/fields/class-acf-field-icon_picker.php:462
msgid "Cart Icon"
msgstr "Иконка Корзина"

#: includes/fields/class-acf-field-icon_picker.php:461
msgid "Carrot Icon"
msgstr "Иконка Морковка"

#: includes/fields/class-acf-field-icon_picker.php:458
msgid "Camera Icon"
msgstr "Иконка Камера"

#: includes/fields/class-acf-field-icon_picker.php:457
msgid "Calendar (alt) Icon"
msgstr "Значок: Календарь (alt)"

#: includes/fields/class-acf-field-icon_picker.php:456
msgid "Calendar Icon"
msgstr "Значок: календарь"

#: includes/fields/class-acf-field-icon_picker.php:453
msgid "Businesswoman Icon"
msgstr "Значок: Бизнесвумен"

#: includes/fields/class-acf-field-icon_picker.php:450
msgid "Building Icon"
msgstr "Значок: Здание"

#: includes/fields/class-acf-field-icon_picker.php:437
msgid "Book Icon"
msgstr "Иконка Книга"

#: includes/fields/class-acf-field-icon_picker.php:432
msgid "Backup Icon"
msgstr "Иконка Резервная копия"

#: includes/fields/class-acf-field-icon_picker.php:431
msgid "Awards Icon"
msgstr "Иконка Награды"

#: includes/fields/class-acf-field-icon_picker.php:430
msgid "Art Icon"
msgstr "Значок: арт объект"

#: includes/fields/class-acf-field-icon_picker.php:427
msgid "Arrow Up Icon"
msgstr "Значок: стрелка вверх"

#: includes/fields/class-acf-field-icon_picker.php:424
msgid "Arrow Right Icon"
msgstr "Значок: Стрелка вправо"

#: includes/fields/class-acf-field-icon_picker.php:421
msgid "Arrow Left Icon"
msgstr "Значок: Стрелка влево"

#: includes/fields/class-acf-field-icon_picker.php:418
msgid "Arrow Down Icon"
msgstr "Значок: стрелка вниз"

#: includes/fields/class-acf-field-icon_picker.php:417
#: includes/fields/class-acf-field-icon_picker.php:601
msgid "Archive Icon"
msgstr "Значок: Архив"

#: includes/fields/class-acf-field-icon_picker.php:416
msgid "Analytics Icon"
msgstr "Значок: Аналитика"

#: includes/fields/class-acf-field-icon_picker.php:413
#: includes/fields/class-acf-field-icon_picker.php:503
msgid "Align Right Icon"
msgstr "Значок: Выравнивание по правому краю"

#: includes/fields/class-acf-field-icon_picker.php:410
msgid "Align None Icon"
msgstr "Значок: Без выравнивания"

#: includes/fields/class-acf-field-icon_picker.php:409
#: includes/fields/class-acf-field-icon_picker.php:502
msgid "Align Left Icon"
msgstr "Значок: Выравнивание по левому краю"

#: includes/fields/class-acf-field-icon_picker.php:407
#: includes/fields/class-acf-field-icon_picker.php:501
msgid "Align Center Icon"
msgstr "Значок: Выравнивание по центру"

#: includes/fields/class-acf-field-icon_picker.php:406
msgid "Album Icon"
msgstr "Иконка Альбом"

#: includes/fields/class-acf-field-icon_picker.php:404
msgid "Users Icon"
msgstr "Иконка Пользователи"

#: includes/fields/class-acf-field-icon_picker.php:403
msgid "Tools Icon"
msgstr "Иконка Инструменты"

#: includes/fields/class-acf-field-icon_picker.php:399
msgid "Site Icon"
msgstr "Иконка Сайт"

#: includes/fields/class-acf-field-icon_picker.php:398
msgid "Settings Icon"
msgstr "Иконка Настройки"

#: includes/fields/class-acf-field-icon_picker.php:397
msgid "Post Icon"
msgstr "Иконка Запись"

#: includes/fields/class-acf-field-icon_picker.php:396
msgid "Plugins Icon"
msgstr "Иконка Плагины"

#: includes/fields/class-acf-field-icon_picker.php:395
msgid "Page Icon"
msgstr "Иконка Страница"

#: includes/fields/class-acf-field-icon_picker.php:394
msgid "Network Icon"
msgstr "Иконка Сеть"

#: includes/fields/class-acf-field-icon_picker.php:393
msgid "Multisite Icon"
msgstr "Иконка Мультисайт"

#: includes/fields/class-acf-field-icon_picker.php:392
msgid "Media Icon"
msgstr "Иконка Медиа"

#: includes/fields/class-acf-field-icon_picker.php:391
msgid "Links Icon"
msgstr "Иконка Ссылки"

#: includes/fields/class-acf-field-icon_picker.php:390
msgid "Home Icon"
msgstr "Иконка Дом"

#: includes/fields/class-acf-field-icon_picker.php:388
msgid "Customizer Icon"
msgstr "Значок: персонализация"

#: includes/fields/class-acf-field-icon_picker.php:387
#: includes/fields/class-acf-field-icon_picker.php:711
msgid "Comments Icon"
msgstr "Иконка Комментарии"

#: includes/fields/class-acf-field-icon_picker.php:386
msgid "Collapse Icon"
msgstr "Иконка Свернуть"

#: includes/fields/class-acf-field-icon_picker.php:385
msgid "Appearance Icon"
msgstr "Иконка Внешний вид"

#: includes/fields/class-acf-field-icon_picker.php:389
msgid "Generic Icon"
msgstr "Значок: общий"

#: includes/fields/class-acf-field-icon_picker.php:321
msgid "Icon picker requires a value."
msgstr "Подборщик иконки требует значение."

#: includes/fields/class-acf-field-icon_picker.php:316
msgid "Icon picker requires an icon type."
msgstr "Подборщик иконки требует тип иконки."

#: includes/fields/class-acf-field-icon_picker.php:285
msgid ""
"The available icons matching your search query have been updated in the icon "
"picker below."
msgstr ""
"Доступные иконки, соответствующие вашему поисковому запросу, были обновлены "
"в подборщике иконки ниже."

#: includes/fields/class-acf-field-icon_picker.php:284
msgid "No results found for that search term"
msgstr "Не найдено результатов по этому поисковому запросу"

#: includes/fields/class-acf-field-icon_picker.php:266
msgid "Array"
msgstr "Массив"

#: includes/fields/class-acf-field-icon_picker.php:265
msgid "String"
msgstr "Строка"

#. translators: %s - link to documentation
#: includes/fields/class-acf-field-icon_picker.php:253
msgid "Specify the return format for the icon. %s"
msgstr "Укажите формат возвращаемого значения для иконки. %s"

#: includes/fields/class-acf-field-icon_picker.php:238
msgid "Select where content editors can choose the icon from."
msgstr "Укажите откуда редакторы содержимого могут выбирать иконку."

#: includes/fields/class-acf-field-icon_picker.php:211
msgid "The URL to the icon you'd like to use, or svg as Data URI"
msgstr ""
"URLиконки, которую вы хотите использовать или svg в качестве URI данных"

#: includes/fields/class-acf-field-icon_picker.php:194
msgid "Browse Media Library"
msgstr "Открыть библиотеку файлов"

#: includes/fields/class-acf-field-icon_picker.php:185
msgid "The currently selected image preview"
msgstr "Предпросмотр выбранного изображения"

#: includes/fields/class-acf-field-icon_picker.php:176
msgid "Click to change the icon in the Media Library"
msgstr "Нажмите чтобы сменить иконку в библиотеке файлов"

#: includes/fields/class-acf-field-icon_picker.php:142
msgid "Search icons..."
msgstr "Искать иконки..."

#: includes/fields/class-acf-field-icon_picker.php:53
msgid "Media Library"
msgstr "Медиафайлы"

#: includes/fields/class-acf-field-icon_picker.php:49
msgid "Dashicons"
msgstr "Dashicons"

#: includes/fields/class-acf-field-icon_picker.php:26
msgid ""
"An interactive UI for selecting an icon. Select from Dashicons, the media "
"library, or a standalone URL input."
msgstr ""
"Интерактивный интерфейс для подбора иконки. Выбирайте среди Dashicons, из "
"библиотеки файлов или используйте автономный ввод URL."

#: includes/fields/class-acf-field-icon_picker.php:23
msgid "Icon Picker"
msgstr "Подборщик иконки"

#: src/Site_Health/Site_Health.php:776
msgid "JSON Load Paths"
msgstr "Пути загрузки JSON"

#: src/Site_Health/Site_Health.php:770
msgid "JSON Save Paths"
msgstr "Пути сохранения JSON"

#: src/Site_Health/Site_Health.php:761
msgid "Registered ACF Forms"
msgstr "Зарегистрированные ACF формы"

#: src/Site_Health/Site_Health.php:755
msgid "Shortcode Enabled"
msgstr "Шорткод включен"

#: src/Site_Health/Site_Health.php:747
msgid "Field Settings Tabs Enabled"
msgstr "Вкладки настроек поля включены"

#: src/Site_Health/Site_Health.php:739
msgid "Field Type Modal Enabled"
msgstr "Окно типа поля включено"

#: src/Site_Health/Site_Health.php:731
msgid "Admin UI Enabled"
msgstr "Интерфейс администратора включен"

#: src/Site_Health/Site_Health.php:722
msgid "Block Preloading Enabled"
msgstr "Предварительная загрузка блока включена"

#: src/Site_Health/Site_Health.php:710
msgid "Blocks Per ACF Block Version"
msgstr ""

#: src/Site_Health/Site_Health.php:705
msgid "Blocks Per API Version"
msgstr "Блоки по версии API"

#: src/Site_Health/Site_Health.php:678
msgid "Registered ACF Blocks"
msgstr "Зарегистрированные ACF блоки"

#: src/Site_Health/Site_Health.php:672
msgid "Light"
msgstr "Светлый"

#: src/Site_Health/Site_Health.php:672
msgid "Standard"
msgstr "Стадартный"

#: src/Site_Health/Site_Health.php:671
msgid "REST API Format"
msgstr "Формат REST API"

#: src/Site_Health/Site_Health.php:663
msgid "Registered Options Pages (PHP)"
msgstr "Зарегистрированные страницы настроек (PHP)"

#: src/Site_Health/Site_Health.php:649
msgid "Registered Options Pages (JSON)"
msgstr "Зарегистрированные страницы настроек (JSON)"

#: src/Site_Health/Site_Health.php:644
msgid "Registered Options Pages (UI)"
msgstr "Зарегистрированные страницы настроек (UI)"

#: src/Site_Health/Site_Health.php:614
msgid "Options Pages UI Enabled"
msgstr "Интерфейс страниц настроек включен"

#: src/Site_Health/Site_Health.php:606
msgid "Registered Taxonomies (JSON)"
msgstr "Зарегистрированные таксономии (JSON)"

#: src/Site_Health/Site_Health.php:594
msgid "Registered Taxonomies (UI)"
msgstr "Зарегистрированные таксономии (UI)"

#: src/Site_Health/Site_Health.php:582
msgid "Registered Post Types (JSON)"
msgstr "Зарегистрированные типы записей (JSON)"

#: src/Site_Health/Site_Health.php:570
msgid "Registered Post Types (UI)"
msgstr "Зарегистрированные типы записей (UI)"

#: src/Site_Health/Site_Health.php:557
msgid "Post Types and Taxonomies Enabled"
msgstr "Типы записей и таксономии включены"

#: src/Site_Health/Site_Health.php:550
msgid "Number of Third Party Fields by Field Type"
msgstr "Кол-во сторонних полей по типу поля"

#: src/Site_Health/Site_Health.php:545
msgid "Number of Fields by Field Type"
msgstr "Кол-во полей по типу поля"

#: src/Site_Health/Site_Health.php:444
msgid "Field Groups Enabled for GraphQL"
msgstr "Группы полей включены для GraphQL"

#: src/Site_Health/Site_Health.php:431
msgid "Field Groups Enabled for REST API"
msgstr "Группы полей включены для REST API"

#: src/Site_Health/Site_Health.php:419
msgid "Registered Field Groups (JSON)"
msgstr "Зарегистрированные группы полей (JSON)"

#: src/Site_Health/Site_Health.php:407
msgid "Registered Field Groups (PHP)"
msgstr "Зарегистрированные группы полей (PHP)"

#: src/Site_Health/Site_Health.php:395
msgid "Registered Field Groups (UI)"
msgstr "Зарегистрированные группы полей (UI)"

#: src/Site_Health/Site_Health.php:383
msgid "Active Plugins"
msgstr "Активные плагины"

#: src/Site_Health/Site_Health.php:357
msgid "Parent Theme"
msgstr "Родительская тема"

#: src/Site_Health/Site_Health.php:346
msgid "Active Theme"
msgstr "Активная тема"

#: src/Site_Health/Site_Health.php:337
msgid "Is Multisite"
msgstr "Является мультисайтом"

#: src/Site_Health/Site_Health.php:332
msgid "MySQL Version"
msgstr "Версия MySQL"

#: src/Site_Health/Site_Health.php:327
msgid "WordPress Version"
msgstr "Версия WordPress"

#: src/Site_Health/Site_Health.php:320
msgid "Subscription Expiry Date"
msgstr "Дата окончания подписки"

#: src/Site_Health/Site_Health.php:312
msgid "License Status"
msgstr "Статус лицензии"

#: src/Site_Health/Site_Health.php:307
msgid "License Type"
msgstr "Тип лицензии"

#: src/Site_Health/Site_Health.php:302
msgid "Licensed URL"
msgstr "URL лицензии"

#: src/Site_Health/Site_Health.php:296
msgid "License Activated"
msgstr "Лицензия активирована"

#: src/Site_Health/Site_Health.php:290
msgid "Free"
msgstr "Бесплатный"

#: src/Site_Health/Site_Health.php:289
msgid "Plugin Type"
msgstr "Тип плагина"

#: src/Site_Health/Site_Health.php:284
msgid "Plugin Version"
msgstr "Версия плагина"

#: src/Site_Health/Site_Health.php:255
msgid ""
"This section contains debug information about your ACF configuration which "
"can be useful to provide to support."
msgstr ""
"В этом разделе содержится отладочная информация о конфигурации ACF, которая "
"может быть полезна для предоставления в службу поддержки."

#: includes/assets.php:373
msgid "An ACF Block on this page requires attention before you can save."
msgstr ""
"Блок ACF на этой странице требует внимания, прежде чем вы сможете сохранить."

#. translators: %s - The clear log button opening HTML tag. %s - The closing
#. HTML tag.
#: includes/admin/views/escaped-html-notice.php:63
msgid ""
"This data is logged as we detect values that have been changed during "
"output. %1$sClear log and dismiss%2$s after escaping the values in your "
"code. The notice will reappear if we detect changed values again."
msgstr ""
"Эти данные записываются в журнал, когда мы обнаруживаем значения, которые "
"были изменены во время вывода. %1$sОчистите журнал и закройте "
"уведомление%2$s после экранирования значений в вашем коде. Уведомление "
"появится снова, если мы снова обнаружим измененные значения."

#: includes/admin/views/escaped-html-notice.php:25
msgid "Dismiss permanently"
msgstr "Отклонить навсегда"

#: includes/admin/views/acf-field-group/field.php:220
msgid "Instructions for content editors. Shown when submitting data."
msgstr "Инструкции для редакторов. Отображается при отправке данных."

#: includes/admin/post-types/admin-field-group.php:143
msgid "Has no term selected"
msgstr "Нет выбранных терминов"

#: includes/admin/post-types/admin-field-group.php:142
msgid "Has any term selected"
msgstr "Имеет выбранный термин"

#: includes/admin/post-types/admin-field-group.php:141
msgid "Terms do not contain"
msgstr "Термин не содержит"

#: includes/admin/post-types/admin-field-group.php:140
msgid "Terms contain"
msgstr "Термины содержат"

#: includes/admin/post-types/admin-field-group.php:139
msgid "Term is not equal to"
msgstr "Термин не равен"

#: includes/admin/post-types/admin-field-group.php:138
msgid "Term is equal to"
msgstr "Термин равен"

#: includes/admin/post-types/admin-field-group.php:137
msgid "Has no user selected"
msgstr "Нет выбранных пользователей"

#: includes/admin/post-types/admin-field-group.php:136
msgid "Has any user selected"
msgstr "Имеет выбранного пользователя"

#: includes/admin/post-types/admin-field-group.php:135
msgid "Users do not contain"
msgstr "Пользователь не содержит"

#: includes/admin/post-types/admin-field-group.php:134
msgid "Users contain"
msgstr "Пользователи содержат"

#: includes/admin/post-types/admin-field-group.php:133
msgid "User is not equal to"
msgstr "Пользователь не равен"

#: includes/admin/post-types/admin-field-group.php:132
msgid "User is equal to"
msgstr "Пользователь равен"

#: includes/admin/post-types/admin-field-group.php:131
msgid "Has no page selected"
msgstr "Нет выбранных страниц"

#: includes/admin/post-types/admin-field-group.php:130
msgid "Has any page selected"
msgstr "Имеет выбранную страницу"

#: includes/admin/post-types/admin-field-group.php:129
msgid "Pages do not contain"
msgstr "Страница не содержит"

#: includes/admin/post-types/admin-field-group.php:128
msgid "Pages contain"
msgstr "Страницы содержат"

#: includes/admin/post-types/admin-field-group.php:127
msgid "Page is not equal to"
msgstr "Запись не равно"

#: includes/admin/post-types/admin-field-group.php:126
msgid "Page is equal to"
msgstr "Запись не равна"

#: includes/admin/post-types/admin-field-group.php:125
msgid "Has no relationship selected"
msgstr "Не выбрана ни одна взаимосвязь"

#: includes/admin/post-types/admin-field-group.php:124
msgid "Has any relationship selected"
msgstr "Имеет выбранную взаимосвязь"

#: includes/admin/post-types/admin-field-group.php:123
msgid "Has no post selected"
msgstr "Нет выбранных записей"

#: includes/admin/post-types/admin-field-group.php:122
msgid "Has any post selected"
msgstr "Имеет выбранную запись"

#: includes/admin/post-types/admin-field-group.php:121
msgid "Posts do not contain"
msgstr "Запись не содержит"

#: includes/admin/post-types/admin-field-group.php:120
msgid "Posts contain"
msgstr "Записи содержат"

#: includes/admin/post-types/admin-field-group.php:119
msgid "Post is not equal to"
msgstr "Запись не равна"

#: includes/admin/post-types/admin-field-group.php:118
msgid "Post is equal to"
msgstr "Запись равна"

#: includes/admin/post-types/admin-field-group.php:117
msgid "Relationships do not contain"
msgstr "Взаимосвязь не содержит"

#: includes/admin/post-types/admin-field-group.php:116
msgid "Relationships contain"
msgstr "Взаимосвязь содержит"

#: includes/admin/post-types/admin-field-group.php:115
msgid "Relationship is not equal to"
msgstr "Взаимосвязь не равна"

#: includes/admin/post-types/admin-field-group.php:114
msgid "Relationship is equal to"
msgstr "Взаимосвязь равна"

#: src/Blocks/Bindings.php:38
msgctxt "The core ACF block binding source name for fields on the current page"
msgid "ACF Fields"
msgstr "Поля ACF"

#: includes/admin/views/browse-fields-modal.php:14
msgid "ACF PRO Feature"
msgstr "Функция ACF PRO"

#: includes/admin/views/browse-fields-modal.php:10
msgid "Renew PRO to Unlock"
msgstr "Возобновить PRO чтобы разблокировать"

#: includes/admin/views/browse-fields-modal.php:8
msgid "Renew PRO License"
msgstr "Возобновить лицензию PRO"

#: includes/admin/views/acf-field-group/field.php:41
msgid "PRO fields cannot be edited without an active license."
msgstr "PRO-поля не могут быть отредактированы без активной лицензии"

#: includes/admin/admin-internal-post-type-list.php:232
msgid ""
"Please activate your ACF PRO license to edit field groups assigned to an ACF "
"Block."
msgstr ""
"Пожалуйста, активируйте лицензию ACF PRO чтобы редактировать группы полей "
"которые привязаны к ACF-блокам."

#: includes/admin/admin-internal-post-type-list.php:231
msgid "Please activate your ACF PRO license to edit this options page."
msgstr ""
"Пожалуйста, активируйте вашу лицензию ACF PRO чтобы редактировать эту "
"страницу настроек."

#: includes/api/api-template.php:385 includes/api/api-template.php:439
msgid ""
"Returning escaped HTML values is only possible when format_value is also "
"true. The field values have not been returned for security."
msgstr ""
"Возврат экранированных значений HTML возможен только если \"format_value\" "
"также равен \"true\". Значение поля не будет возвращено в целях безопасности."

#: includes/api/api-template.php:46 includes/api/api-template.php:251
#: includes/api/api-template.php:947
msgid ""
"Returning an escaped HTML value is only possible when format_value is also "
"true. The field value has not been returned for security."
msgstr ""
"Возврат экранированного значения HTML возможен только если \"format_value\" "
"также равен \"true\". Значение поля не будет возвращено в целях безопасности."

#. translators: %1$s - name of the ACF plugin. %2$s - Link to documentation.
#: includes/admin/views/escaped-html-notice.php:32
msgid ""
"%1$s ACF now automatically escapes unsafe HTML when rendered by "
"<code>the_field</code> or the ACF shortcode. We've detected the output of "
"some of your fields has been modified by this change, but this may not be a "
"breaking change. %2$s."
msgstr ""

#: includes/admin/views/escaped-html-notice.php:27
msgid "Please contact your site administrator or developer for more details."
msgstr ""
"Пожалуйста, свяжитесь с вашим администратором сайта или разработчиком чтобы "
"узнать подробности."

#: includes/admin/views/escaped-html-notice.php:5
msgid "Learn&nbsp;more"
msgstr "Узнать&nbsp;больше"

#: includes/admin/admin.php:63
msgid "Hide&nbsp;details"
msgstr "Скрыть&nbsp;подробности"

#: includes/admin/admin.php:62 includes/admin/views/escaped-html-notice.php:24
msgid "Show&nbsp;details"
msgstr "Показать&nbsp;подробности"

#. translators: %1$s - The selector used  %2$s The field name  3%$s The parent
#. function name
#: includes/admin/views/escaped-html-notice.php:49
msgid "%1$s (%2$s) - rendered via %3$s"
msgstr "%1$s (%2$s) - отрисовано через %3$s"

#: includes/admin/views/global/navigation.php:226
msgid "Renew ACF PRO License"
msgstr "Возобновить лицензию ACF PRO"

#: includes/admin/views/acf-field-group/pro-features.php:17
msgid "Renew License"
msgstr "Продлить лицензию"

#: includes/admin/views/acf-field-group/pro-features.php:14
msgid "Manage License"
msgstr "Управление лицензией"

#: includes/admin/views/acf-field-group/options.php:102
msgid "'High' position not supported in the Block Editor"
msgstr "Расположение 'Сверху' не поддерживается в редакторе блоков"

#: includes/admin/views/options-page-preview.php:30
msgid "Upgrade to ACF PRO"
msgstr "Обновить до ACF PRO"

#. translators: %s URL to ACF options pages documentation
#: includes/admin/views/options-page-preview.php:7
msgid ""
"ACF <a href=\"%s\" target=\"_blank\">options pages</a> are custom admin "
"pages for managing global settings via fields. You can create multiple pages "
"and sub-pages."
msgstr ""

#: includes/admin/views/global/header.php:35
msgid "Add Options Page"
msgstr "Добавить страницу настроек"

#: includes/admin/views/acf-post-type/advanced-settings.php:708
msgid "In the editor used as the placeholder of the title."
msgstr "Используется как текст-заполнитель заголовка в редакторе."

#: includes/admin/views/acf-post-type/advanced-settings.php:707
msgid "Title Placeholder"
msgstr "Текст-заполнитель заголовка"

#: includes/admin/views/global/navigation.php:97
msgid "4 Months Free"
msgstr "4 месяца бесплатно"

#. translators: %s - A singular label for a post type or taxonomy.
#: includes/admin/views/global/form-top.php:59
msgid "(Duplicated from %s)"
msgstr "(Скопировано из %s)"

#: includes/admin/tools/class-acf-admin-tool-export.php:289
msgid "Select Options Pages"
msgstr "Выберите страницы настроек"

#: includes/admin/post-types/admin-taxonomy.php:107
msgid "Duplicate taxonomy"
msgstr "Скопировать таксономию"

#: includes/admin/post-types/admin-post-type.php:106
#: includes/admin/post-types/admin-taxonomy.php:106
msgid "Create taxonomy"
msgstr "Создать таксономию"

#: includes/admin/post-types/admin-post-type.php:105
msgid "Duplicate post type"
msgstr "Скопировать тип записи"

#: includes/admin/post-types/admin-post-type.php:104
#: includes/admin/post-types/admin-taxonomy.php:108
msgid "Create post type"
msgstr "Создать тип записи"

#: includes/admin/post-types/admin-post-type.php:103
#: includes/admin/post-types/admin-taxonomy.php:105
msgid "Link field groups"
msgstr "Привязать группы полей"

#: includes/admin/post-types/admin-post-type.php:102
#: includes/admin/post-types/admin-taxonomy.php:104
msgid "Add fields"
msgstr "Добавить поля"

#: includes/admin/post-types/admin-field-group.php:147
msgid "This Field"
msgstr "Это поле"

#: includes/admin/admin.php:361
msgid "ACF PRO"
msgstr "ACF (PRO)"

#: includes/admin/admin.php:359
msgid "Feedback"
msgstr "Обратная связь"

#: includes/admin/admin.php:357
msgid "Support"
msgstr "Поддержка"

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:332
msgid "is developed and maintained by"
msgstr "разработан и поддерживается"

#. translators: %s - either "post type" or "taxonomy"
#: includes/admin/admin-internal-post-type.php:313
msgid "Add this %s to the location rules of the selected field groups."
msgstr "Добавьте %s в правила местонахождения выбранных групп полей."

#. translators: %s the URL to ACF's bidirectional relationship documentation
#: includes/acf-bidirectional-functions.php:272
msgid ""
"Enabling the bidirectional setting allows you to update a value in the "
"target fields for each value selected for this field, adding or removing the "
"Post ID, Taxonomy ID or User ID of the item being updated. For more "
"information, please read the <a href=\"%s\" target=\"_blank\">documentation</"
"a>."
msgstr ""

#: includes/acf-bidirectional-functions.php:248
msgid ""
"Select field(s) to store the reference back to the item being updated. You "
"may select this field. Target fields must be compatible with where this "
"field is being displayed. For example, if this field is displayed on a "
"Taxonomy, your target field should be of type Taxonomy"
msgstr ""
"Выберите поле(-я) для хранения ссылки на обновляемый элемент. Вы можете "
"выбрать это поле. Целевые поля должны быть совместимы с тем, где "
"отображается это поле. Например, если это поле отображается в таксономии, "
"целевое поле должно иметь тип Таксономия"

#: includes/acf-bidirectional-functions.php:247
msgid "Target Field"
msgstr "Целевое поле"

#: includes/acf-bidirectional-functions.php:221
msgid "Update a field on the selected values, referencing back to this ID"
msgstr "Обновлять поле по выбранным значениям, ссылаясь на этот ID"

#: includes/acf-bidirectional-functions.php:220
msgid "Bidirectional"
msgstr "Двунаправленный"

#. translators: %s A field type name, such as "Relationship"
#: includes/acf-bidirectional-functions.php:193
msgid "%s Field"
msgstr "%s поле"

#: includes/fields/class-acf-field-page_link.php:498
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-select.php:378
#: includes/fields/class-acf-field-user.php:111
msgid "Select Multiple"
msgstr "Выбор нескольких пунктов"

#: includes/admin/views/global/navigation.php:238
msgid "WP Engine logo"
msgstr "Логотип WP Engine"

#: includes/admin/views/acf-taxonomy/basic-settings.php:58
msgid "Lower case letters, underscores and dashes only, Max 32 characters."
msgstr ""
"Только буквы нижнего регистра, подчеркивания и дефисы. Максимум 32 символа."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1156
msgid "The capability name for assigning terms of this taxonomy."
msgstr "Названия прав доступа для присвоения терминов к этой таксономии."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1155
msgid "Assign Terms Capability"
msgstr "Права для присвоения терминов"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1139
msgid "The capability name for deleting terms of this taxonomy."
msgstr "Названия прав доступа для удаления терминов этой таксономии."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1138
msgid "Delete Terms Capability"
msgstr "Права для удаления терминов"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1122
msgid "The capability name for editing terms of this taxonomy."
msgstr "Названия прав доступа для редактирования терминов этой таксономии."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1121
msgid "Edit Terms Capability"
msgstr "Права для редактирования терминов"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1105
msgid "The capability name for managing terms of this taxonomy."
msgstr "Название прав доступа для управления терминами этой таксономии."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1104
msgid "Manage Terms Capability"
msgstr "Возможность управления терминами"

#: includes/admin/views/acf-post-type/advanced-settings.php:929
msgid ""
"Sets whether posts should be excluded from search results and taxonomy "
"archive pages."
msgstr ""
"Устанавливает, должны ли записи быть исключены из результатов поиска и "
"страниц архива таксономии."

#: includes/admin/views/acf-field-group/pro-features.php:78
msgid "More Tools from WP Engine"
msgstr "Больше инструментов от WP Engine"

#. translators: %s - WP Engine logo
#: includes/admin/views/acf-field-group/pro-features.php:73
msgid "Built for those that build with WordPress, by the team at %s"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:6
msgid "View Pricing & Upgrade"
msgstr "Просмотр цен и обновление"

#: includes/admin/views/acf-field-group/pro-features.php:3
#: includes/admin/views/options-page-preview.php:29
#: includes/fields/class-acf-field-icon_picker.php:248
msgid "Learn More"
msgstr "Читать больше"

#: includes/admin/views/acf-field-group/pro-features.php:28
msgid ""
"Speed up your workflow and develop better websites with features like ACF "
"Blocks and Options Pages, and sophisticated field types like Repeater, "
"Flexible Content, Clone, and Gallery."
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:2
msgid "Unlock Advanced Features and Build Even More with ACF PRO"
msgstr "Разблокируйте дополнительные функции и сделайте больше с ACF PRO"

#. translators: %s - singular label of post type/taxonomy, i.e. "Movie"/"Genre"
#: includes/admin/views/global/form-top.php:19
msgid "%s fields"
msgstr "%s поля"

#: includes/admin/post-types/admin-taxonomies.php:267
msgid "No terms"
msgstr "Нет терминов"

#: includes/admin/post-types/admin-taxonomies.php:240
msgid "No post types"
msgstr "Нет типов записей"

#: includes/admin/post-types/admin-post-types.php:264
msgid "No posts"
msgstr "Нет записей"

#: includes/admin/post-types/admin-post-types.php:238
msgid "No taxonomies"
msgstr "Нет таксономий"

#: includes/admin/post-types/admin-post-types.php:183
#: includes/admin/post-types/admin-taxonomies.php:182
msgid "No field groups"
msgstr "Нет групп полей"

#: includes/admin/post-types/admin-field-groups.php:255
msgid "No fields"
msgstr "Нет полей"

#: includes/admin/post-types/admin-field-groups.php:128
#: includes/admin/post-types/admin-post-types.php:147
#: includes/admin/post-types/admin-taxonomies.php:146
msgid "No description"
msgstr "Нет описания"

#: includes/fields/class-acf-field-page_link.php:465
#: includes/fields/class-acf-field-post_object.php:374
#: includes/fields/class-acf-field-relationship.php:573
msgid "Any post status"
msgstr "Любой статус записи"

#: includes/post-types/class-acf-taxonomy.php:288
msgid ""
"This taxonomy key is already in use by another taxonomy registered outside "
"of ACF and cannot be used."
msgstr ""
"Указанный ключ таксономии уже используется другой таксономией, "
"зарегистрированной вне ACF, и не может быть использован."

#: includes/post-types/class-acf-taxonomy.php:284
msgid ""
"This taxonomy key is already in use by another taxonomy in ACF and cannot be "
"used."
msgstr ""
"Указанный ключ таксономии уже используется другой таксономией в ACF и не "
"может быть использован."

#: includes/post-types/class-acf-taxonomy.php:256
msgid ""
"The taxonomy key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"Ключ таксономии должен содержать только буквенно-цифровые символы в нижнем "
"регистре, знак подчеркивания или тире."

#: includes/post-types/class-acf-taxonomy.php:251
msgid "The taxonomy key must be under 32 characters."
msgstr "Ключ таксономии должен содержать не более 32 символов."

#: includes/post-types/class-acf-taxonomy.php:99
msgid "No Taxonomies found in Trash"
msgstr "В корзине не найдено ни одной таксономии"

#: includes/post-types/class-acf-taxonomy.php:98
msgid "No Taxonomies found"
msgstr "Таксономии не найдены"

#: includes/post-types/class-acf-taxonomy.php:97
msgid "Search Taxonomies"
msgstr "Найти таксономии"

#: includes/post-types/class-acf-taxonomy.php:96
msgid "View Taxonomy"
msgstr "Смотреть таксономию"

#: includes/post-types/class-acf-taxonomy.php:95
msgid "New Taxonomy"
msgstr "Новая таксономия"

#: includes/post-types/class-acf-taxonomy.php:94
msgid "Edit Taxonomy"
msgstr "Править таксономию"

#: includes/post-types/class-acf-taxonomy.php:93
msgid "Add New Taxonomy"
msgstr "Добавить новую таксономию"

#: includes/post-types/class-acf-post-type.php:100
msgid "No Post Types found in Trash"
msgstr "В корзине не найдено ни одного типа записей"

#: includes/post-types/class-acf-post-type.php:99
msgid "No Post Types found"
msgstr "Типы записей не найдены"

#: includes/post-types/class-acf-post-type.php:98
msgid "Search Post Types"
msgstr "Найти типы записей"

#: includes/post-types/class-acf-post-type.php:97
msgid "View Post Type"
msgstr "Смотреть тип записи"

#: includes/post-types/class-acf-post-type.php:96
msgid "New Post Type"
msgstr "Новый тип записи"

#: includes/post-types/class-acf-post-type.php:95
msgid "Edit Post Type"
msgstr "Изменить тип записи"

#: includes/post-types/class-acf-post-type.php:94
msgid "Add New Post Type"
msgstr "Добавить новый тип записи"

#: includes/post-types/class-acf-post-type.php:366
msgid ""
"This post type key is already in use by another post type registered outside "
"of ACF and cannot be used."
msgstr ""
"Указанный ключ типа записи уже используется другим типом записи, "
"зарегистрированным вне ACF, и не может быть использован."

#: includes/post-types/class-acf-post-type.php:361
msgid ""
"This post type key is already in use by another post type in ACF and cannot "
"be used."
msgstr ""
"Указанный ключ типа записи уже используется другим типом записи в ACF и не "
"может быть использован."

#. translators: %s a link to WordPress.org's Reserved Terms page
#: includes/post-types/class-acf-post-type.php:339
#: includes/post-types/class-acf-taxonomy.php:262
msgid ""
"This field must not be a WordPress <a href=\"%s\" target=\"_blank\">reserved "
"term</a>."
msgstr ""
"Это поле является <a href=\"%s\" target=\"_blank\">зарезервированным "
"термином</a> WordPress."

#: includes/post-types/class-acf-post-type.php:333
msgid ""
"The post type key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"Ключ типа записи должен содержать только буквенно-цифровые символы в нижнем "
"регистре, знак подчеркивания или тире."

#: includes/post-types/class-acf-post-type.php:328
msgid "The post type key must be under 20 characters."
msgstr "Ключ типа записи должен содержать не более 20 символов."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid "We do not recommend using this field in ACF Blocks."
msgstr "Мы не рекомендуем использовать это поле в блоках ACF."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid ""
"Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing "
"for a rich text-editing experience that also allows for multimedia content."
msgstr ""
"Показывает WordPress WYSIWYG редактор такой же, как в Записях или Страницах "
"и позволяет редактировать текст, а также мультимедийное содержимое."

#: includes/fields/class-acf-field-wysiwyg.php:22
msgid "WYSIWYG Editor"
msgstr "WYSIWYG редактор"

#: includes/fields/class-acf-field-user.php:17
msgid ""
"Allows the selection of one or more users which can be used to create "
"relationships between data objects."
msgstr ""
"Позволяет выбрать одного или нескольких пользователей, которые могут быть "
"использованы для создания взаимосвязей между объектами данных."

#: includes/fields/class-acf-field-url.php:20
msgid "A text input specifically designed for storing web addresses."
msgstr "Текстовый поле, специально разработанное для хранения веб-адресов."

#: includes/fields/class-acf-field-icon_picker.php:56
#: includes/fields/class-acf-field-url.php:19
msgid "URL"
msgstr "URL"

#: includes/fields/class-acf-field-true_false.php:24
msgid ""
"A toggle that allows you to pick a value of 1 or 0 (on or off, true or "
"false, etc). Can be presented as a stylized switch or checkbox."
msgstr ""
"Переключатель, позволяющий выбрать значение 1 или 0 (включено или выключено, "
"истинно или ложно и т.д.). Может быть представлен в виде стилизованного "
"переключателя или флажка."

#: includes/fields/class-acf-field-time_picker.php:24
msgid ""
"An interactive UI for picking a time. The time format can be customized "
"using the field settings."
msgstr ""
"Интерактивный пользовательский интерфейс для выбора времени. Формат времени "
"можно настроить с помощью параметров поля."

#: includes/fields/class-acf-field-textarea.php:23
msgid "A basic textarea input for storing paragraphs of text."
msgstr "Простая текстовая область для хранения абзацев текста."

#: includes/fields/class-acf-field-text.php:23
msgid "A basic text input, useful for storing single string values."
msgstr ""
"Простое текстовое поле, предназначенное для хранения однострочных значений."

#: includes/fields/class-acf-field-taxonomy.php:22
msgid ""
"Allows the selection of one or more taxonomy terms based on the criteria and "
"options specified in the fields settings."
msgstr ""
"Позволяет выбрать один или несколько терминов таксономии на основе критериев "
"и параметров, указанных в настройках полей."

#: includes/fields/class-acf-field-tab.php:25
msgid ""
"Allows you to group fields into tabbed sections in the edit screen. Useful "
"for keeping fields organized and structured."
msgstr ""
"Позволяет группировать поля в разделы с вкладками на экране редактирования. "
"Полезно для упорядочивания и структурирования полей."

#: includes/fields/class-acf-field-select.php:24
msgid "A dropdown list with a selection of choices that you specify."
msgstr "Выпадающий список с заданными вариантами выбора."

#: includes/fields/class-acf-field-relationship.php:19
msgid ""
"A dual-column interface to select one or more posts, pages, or custom post "
"type items to create a relationship with the item that you're currently "
"editing. Includes options to search and filter."
msgstr ""
"Двухколоночный интерфейс для выбора одной или нескольких записей, страниц "
"или элементов пользовательских типов записей, чтобы создать связь с "
"элементом, который вы сейчас редактируете. Включает опции поиска и "
"фильтрации."

#: includes/fields/class-acf-field-range.php:23
msgid ""
"An input for selecting a numerical value within a specified range using a "
"range slider element."
msgstr ""
"Поле выбора числового значения в заданном диапазоне с помощью ползунка "
"диапазона."

#: includes/fields/class-acf-field-radio.php:24
msgid ""
"A group of radio button inputs that allows the user to make a single "
"selection from values that you specify."
msgstr ""
"Группа радиокнопок, позволяющих пользователю выбрать одно из заданных вами "
"значений."

#: includes/fields/class-acf-field-post_object.php:17
msgid ""
"An interactive and customizable UI for picking one or many posts, pages or "
"post type items with the option to search. "
msgstr ""
"Интерактивный и настраиваемый интерфейс для выбора одной или нескольких "
"записей, страниц или типов записей с возможностью поиска. "

#: includes/fields/class-acf-field-password.php:23
msgid "An input for providing a password using a masked field."
msgstr "Ввод пароля с помощью замаскированного поля."

#: includes/fields/class-acf-field-page_link.php:457
#: includes/fields/class-acf-field-post_object.php:366
#: includes/fields/class-acf-field-relationship.php:565
msgid "Filter by Post Status"
msgstr "Фильтр по статусу записи"

#: includes/fields/class-acf-field-page_link.php:24
msgid ""
"An interactive dropdown to select one or more posts, pages, custom post type "
"items or archive URLs, with the option to search."
msgstr ""
"Интерактивный выпадающий список для выбора одного или нескольких записей, "
"страниц, записей пользовательского типа или URL-адресов архивов с "
"возможностью поиска."

#: includes/fields/class-acf-field-oembed.php:24
msgid ""
"An interactive component for embedding videos, images, tweets, audio and "
"other content by making use of the native WordPress oEmbed functionality."
msgstr ""
"Интерактивный компонент для вставки видео, изображений, твитов, аудио и "
"другого контента с использованием встроенной в WordPress функциональности "
"oEmbed."

#: includes/fields/class-acf-field-number.php:23
msgid "An input limited to numerical values."
msgstr "Ввод ограничен числовыми значениями."

#: includes/fields/class-acf-field-message.php:25
msgid ""
"Used to display a message to editors alongside other fields. Useful for "
"providing additional context or instructions around your fields."
msgstr ""
"Используется для отображения сообщения для редакторов рядом с другими "
"полями. Полезно для предоставления дополнительного контекста или инструкций "
"по работе с полями."

#: includes/fields/class-acf-field-link.php:24
msgid ""
"Allows you to specify a link and its properties such as title and target "
"using the WordPress native link picker."
msgstr ""
"Позволяет указать ссылку и ее свойства, такие как заголовок и цель, "
"используя встроенный в WordPress подборщик ссылок."

#: includes/fields/class-acf-field-image.php:24
msgid "Uses the native WordPress media picker to upload, or choose images."
msgstr ""
"Использует встроенный в WordPress подборщик медиа для загрузки или выбора "
"изображений."

#: includes/fields/class-acf-field-group.php:24
msgid ""
"Provides a way to structure fields into groups to better organize the data "
"and the edit screen."
msgstr ""
"Предоставляет возможность структурировать поля по группам, чтобы лучше "
"организовать данные и экран редактирования."

#: includes/fields/class-acf-field-google-map.php:24
msgid ""
"An interactive UI for selecting a location using Google Maps. Requires a "
"Google Maps API key and additional configuration to display correctly."
msgstr ""
"Интерактивный интерфейс для выбора местоположения с помощью Google Maps. "
"Требуется ключ Google Maps API и дополнительная настройка для корректного "
"отображения."

#: includes/fields/class-acf-field-file.php:24
msgid "Uses the native WordPress media picker to upload, or choose files."
msgstr ""
"Использует встроенный в WordPress медиа подборщик чтобы загружать или "
"выбирать файлы."

#: includes/fields/class-acf-field-email.php:23
msgid "A text input specifically designed for storing email addresses."
msgstr ""
"Текстовый ввод, специально предназначенный для хранения адресов электронной "
"почты."

#: includes/fields/class-acf-field-date_time_picker.php:24
msgid ""
"An interactive UI for picking a date and time. The date return format can be "
"customized using the field settings."
msgstr ""
"Интерактивный интерфейс для выбора времени. Формат времени можно настроить с "
"помощью параметров поля."

#: includes/fields/class-acf-field-date_picker.php:24
msgid ""
"An interactive UI for picking a date. The date return format can be "
"customized using the field settings."
msgstr ""
"Интерактивный интерфейс для выбора даты. Возвращаемый формат даты можно "
"настроить с помощью параметров поля."

#: includes/fields/class-acf-field-color_picker.php:24
msgid "An interactive UI for selecting a color, or specifying a Hex value."
msgstr "Интерактивный интерфейс для выбора цвета или указания Hex значения."

#: includes/fields/class-acf-field-checkbox.php:24
msgid ""
"A group of checkbox inputs that allow the user to select one, or multiple "
"values that you specify."
msgstr ""
"Группа полей с флажками, которые позволяют пользователю выбрать одно или "
"несколько из заданных вами значений."

#: includes/fields/class-acf-field-button-group.php:25
msgid ""
"A group of buttons with values that you specify, users can choose one option "
"from the values provided."
msgstr ""
"Группа кнопок с указанными вами значениями, пользователи могут выбрать одну "
"опцию из представленных значений."

#: includes/fields/class-acf-field-accordion.php:26
msgid ""
"Allows you to group and organize custom fields into collapsable panels that "
"are shown while editing content. Useful for keeping large datasets tidy."
msgstr ""
"Позволяет ваш группировать и организовывать пользовательские поля в "
"сворачиваемые панели, которые отображаются при редактировании содержимого. "
"Полезно для поддержания порядка в больших наборах данных."

#: includes/fields.php:449
msgid ""
"This provides a solution for repeating content such as slides, team members, "
"and call-to-action tiles, by acting as a parent to a set of subfields which "
"can be repeated again and again."
msgstr ""
"Обеспечивает решение для повторения содержимого, такого как слайды, члены "
"команды и плитки с призывами к действию, выступая в качестве родителя для "
"набора вложенных полей, которые можно повторять снова и снова."

#: includes/fields.php:439
msgid ""
"This provides an interactive interface for managing a collection of "
"attachments. Most settings are similar to the Image field type. Additional "
"settings allow you to specify where new attachments are added in the gallery "
"and the minimum/maximum number of attachments allowed."
msgstr ""
"Интерактивный интерфейс для управления коллекцией вложений. Большинство "
"настроек аналогично типу поля «Изображение». Дополнительные настройки "
"позволяют указать место добавления новых вложений в галерею и мин/макс "
"количество вложений."

#: includes/fields.php:429
msgid ""
"This provides a simple, structured, layout-based editor. The Flexible "
"Content field allows you to define, create and manage content with total "
"control by using layouts and subfields to design the available blocks."
msgstr ""
"Обеспечивает простой, структурированный редактор на основе макетов. Поле "
"«Гибкое содержимое» позволяет создавать и управлять содержимым с полным "
"контролем, используя макеты и вложенные поля для оформления доступных блоков."

#: includes/fields.php:419
msgid ""
"This allows you to select and display existing fields. It does not duplicate "
"any fields in the database, but loads and displays the selected fields at "
"run-time. The Clone field can either replace itself with the selected fields "
"or display the selected fields as a group of subfields."
msgstr ""
"Позволяет выбирать и отображать существующие поля. Он не дублирует поля в "
"базе данных, а загружает и отображает выбранные поля во время выполнения "
"программы. Поле Дубликатор может либо заменять собой выбранные поля, либо "
"отображать выбранные поля в виде группы вложенных полей."

#: includes/fields.php:416
msgctxt "noun"
msgid "Clone"
msgstr "Клонировать"

#: includes/admin/views/global/navigation.php:86 includes/fields.php:331
#: src/Site_Health/Site_Health.php:290
msgid "PRO"
msgstr "PRO"

#: includes/fields.php:329 includes/fields.php:386
msgid "Advanced"
msgstr "Дополнительно"

#: includes/ajax/class-acf-ajax-local-json-diff.php:90
msgid "JSON (newer)"
msgstr "JSON (более новая версия)"

#: includes/ajax/class-acf-ajax-local-json-diff.php:86
msgid "Original"
msgstr "Оригинальный"

#: includes/ajax/class-acf-ajax-local-json-diff.php:60
msgid "Invalid post ID."
msgstr "Неверный ID записи."

#: includes/ajax/class-acf-ajax-local-json-diff.php:52
msgid "Invalid post type selected for review."
msgstr "Для просмотра выбран неверный тип записи."

#: includes/admin/views/global/navigation.php:189
msgid "More"
msgstr "Читать далее"

#: includes/admin/views/browse-fields-modal.php:96
msgid "Tutorial"
msgstr "Руководство"

#: includes/admin/views/browse-fields-modal.php:73
msgid "Select Field"
msgstr "Выбрать поля"

#. translators: %s: A link to the popular fields used in ACF
#: includes/admin/views/browse-fields-modal.php:60
msgid "Try a different search term or browse %s"
msgstr "Попробуйте другой поисковый запрос или просмотрите %s"

#: includes/admin/views/browse-fields-modal.php:57
msgid "Popular fields"
msgstr "Популярные поля"

#. translators: %s: The invalid search term
#: includes/admin/views/browse-fields-modal.php:50
#: includes/fields/class-acf-field-icon_picker.php:155
msgid "No search results for '%s'"
msgstr "Нет результатов поиска для '%s'"

#: includes/admin/views/browse-fields-modal.php:23
msgid "Search fields..."
msgstr "Поля поиска..."

#: includes/admin/views/browse-fields-modal.php:21
msgid "Select Field Type"
msgstr "Выбрать тип поля"

#: includes/admin/views/browse-fields-modal.php:4
msgid "Popular"
msgstr "Популярные"

#: includes/admin/views/acf-taxonomy/list-empty.php:15
msgid "Add Taxonomy"
msgstr "Добавить таксономию"

#: includes/admin/views/acf-taxonomy/list-empty.php:14
msgid "Create custom taxonomies to classify post type content"
msgstr ""
"Создание пользовательских таксономий для классификации содержимого типов "
"записей"

#: includes/admin/views/acf-taxonomy/list-empty.php:13
msgid "Add Your First Taxonomy"
msgstr "Добавьте свою первую таксономию"

#: includes/admin/views/acf-taxonomy/basic-settings.php:122
msgid "Hierarchical taxonomies can have descendants (like categories)."
msgstr "Иерархические таксономии могут иметь потомков (например, категории)."

#: includes/admin/views/acf-taxonomy/basic-settings.php:107
msgid "Makes a taxonomy visible on the frontend and in the admin dashboard."
msgstr "Сделать видимой таксономию на фронтенде и в админпанели."

#: includes/admin/views/acf-taxonomy/basic-settings.php:91
msgid "One or many post types that can be classified with this taxonomy."
msgstr ""
"Один или несколько типов записей, которые можно классифицировать с помощью "
"данной таксономии."

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:60
msgid "genre"
msgstr "жанр"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:42
msgid "Genre"
msgstr "Жанр"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:25
msgid "Genres"
msgstr "Жанры"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1231
msgid ""
"Optional custom controller to use instead of `WP_REST_Terms_Controller `."
msgstr ""
"Пользовательский контроллер для использования вместо "
"`WP_REST_Terms_Controller`."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1175
msgid "Expose this post type in the REST API."
msgstr "Представить этот тип записей в REST API."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1075
msgid "Customize the query variable name"
msgstr "Настройте значение переменной запроса."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1048
msgid ""
"Terms can be accessed using the non-pretty permalink, e.g., {query_var}"
"={term_slug}."
msgstr ""
"Доступ к терминам можно получить, используя некрасивую постоянную ссылку, "
"например {query_var}={term_slug}."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1001
msgid "Parent-child terms in URLs for hierarchical taxonomies."
msgstr "Термины «родитель-ребенок» в URL для древовидных таксономий."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:961
msgid "Customize the slug used in the URL"
msgstr "Настроить слаг, используемое в URL"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:944
msgid "Permalinks for this taxonomy are disabled."
msgstr "Постоянные ссылки для этой таксономии отключены."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-taxonomy/advanced-settings.php:941
msgid ""
"Rewrite the URL using the taxonomy key as the slug. Your permalink structure "
"will be"
msgstr ""
"Перепишите URL, используя ключ таксономии в качестве слага. Ваша структура "
"постоянных ссылок будет выглядеть следующим образом"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:933
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1050
#: includes/admin/views/acf-taxonomy/basic-settings.php:57
msgid "Taxonomy Key"
msgstr "Ключ таксономии"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:931
msgid "Select the type of permalink to use for this taxonomy."
msgstr "Выберите тип постоянной ссылки для этой таксономии."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:916
msgid "Display a column for the taxonomy on post type listing screens."
msgstr "Отображать колонку таксономии на страницах списков типа записей."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:915
msgid "Show Admin Column"
msgstr "Отображать столбец админа"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:902
msgid "Show the taxonomy in the quick/bulk edit panel."
msgstr "Отображать таксономию в панели быстрого/массового редактирования."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:901
msgid "Quick Edit"
msgstr "Быстрое редактирование"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:888
msgid "List the taxonomy in the Tag Cloud Widget controls."
msgstr "Выводит таксономию в управлении виджета Облака меток."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:887
msgid "Tag Cloud"
msgstr "Облако меток"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:842
msgid ""
"A PHP function name to be called for sanitizing taxonomy data saved from a "
"meta box."
msgstr ""
"Название PHP-функции, вызываемой для санации данных таксономии, сохраненных "
"из мета-бокса."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:841
msgid "Meta Box Sanitization Callback"
msgstr "Колбэк-функция очистки данных метабокса"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:821
msgid "Register Meta Box Callback"
msgstr "Регистрация обратного вызова метабокса"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:752
msgid "No Meta Box"
msgstr "Отсутствует метабокс"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:751
msgid "Custom Meta Box"
msgstr "Произвольный метабокс"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:768
msgid ""
"Controls the meta box on the content editor screen. By default, the "
"Categories meta box is shown for hierarchical taxonomies, and the Tags meta "
"box is shown for non-hierarchical taxonomies."
msgstr ""
"Управляет метабоксом на странице редактирования. По умолчанию для "
"древовидных таксономий отображается метабокс Рубрики, а для недревовидных - "
"метабокс Метки."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:767
msgid "Meta Box"
msgstr "Блок метаданных"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:746
#: includes/admin/views/acf-taxonomy/advanced-settings.php:773
msgid "Categories Meta Box"
msgstr "Категории метабокса"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:745
#: includes/admin/views/acf-taxonomy/advanced-settings.php:772
msgid "Tags Meta Box"
msgstr "Теги метабокса"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:704
msgid "A link to a tag"
msgstr "Ссылка на метку"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:703
msgid "Describes a navigation link block variation used in the block editor."
msgstr ""
"Описывает вариацию блока навигационных ссылок, используемому в редакторе "
"блоков."

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:698
msgid "A link to a %s"
msgstr "Ссылка на %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:683
msgid "Tag Link"
msgstr "Ссылка метки"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:682
msgid ""
"Assigns a title for navigation link block variation used in the block editor."
msgstr ""
"Назначает заголовок вариации блока навигационных ссылок, используемому в "
"редакторе блоков."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:663
msgid "← Go to tags"
msgstr "← Перейти к меткам"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:662
msgid ""
"Assigns the text used to link back to the main index after updating a term."
msgstr ""
"Назначает текст, используемый для обратной ссылки на основной индекс после "
"обновления термина."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:661
msgid "Back To Items"
msgstr "Вернуться к элементам"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:657
msgid "← Go to %s"
msgstr "← Перейти к %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:642
msgid "Tags list"
msgstr "Список меток"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:641
msgid "Assigns text to the table hidden heading."
msgstr "Присваивает текст скрытому заголовку таблицы."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:622
msgid "Tags list navigation"
msgstr "Навигация по списку меток"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:621
msgid "Assigns text to the table pagination hidden heading."
msgstr "Присваивает текст скрытому заголовку таблицы пагинации."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:597
msgid "Filter by category"
msgstr "Фильтр по рубрике"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:596
msgid "Assigns text to the filter button in the posts lists table."
msgstr "Назначает текст для кнопки фильтрации в таблице списков записей."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:595
msgid "Filter By Item"
msgstr "Фильтр по элементу"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:591
msgid "Filter by %s"
msgstr "Фильтр по %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:575
#: includes/admin/views/acf-taxonomy/advanced-settings.php:576
msgid ""
"The description is not prominent by default; however, some themes may show "
"it."
msgstr ""
"Описание по умолчанию не отображается, однако некоторые темы могут его "
"показывать."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:574
msgid "Describes the Description field on the Edit Tags screen."
msgstr "Описывает поле «Описание» на странице редактирования меток."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:573
msgid "Description Field Description"
msgstr "Описание поля описания"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:554
#: includes/admin/views/acf-taxonomy/advanced-settings.php:555
msgid ""
"Assign a parent term to create a hierarchy. The term Jazz, for example, "
"would be the parent of Bebop and Big Band"
msgstr ""
"Назначьте родительский термин для создания иерархии. Термин \"Джаз\", "
"например, будет родителем для \"Бибопа\" и \"Биг-бэнда\"."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:553
msgid "Describes the Parent field on the Edit Tags screen."
msgstr "Описывает поле «Родитель» на странице редактирования меток."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:552
msgid "Parent Field Description"
msgstr "Описание родительского поля"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:538
#: includes/admin/views/acf-taxonomy/advanced-settings.php:539
msgid ""
"The \"slug\" is the URL-friendly version of the name. It is usually all "
"lower case and contains only letters, numbers, and hyphens."
msgstr ""
"«Ярлык» — это вариант названия, подходящий для URL. Обычно содержит только "
"латинские буквы в нижнем регистре, цифры и дефисы."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:537
msgid "Describes the Slug field on the Edit Tags screen."
msgstr "Описывает поле «Ярлык» на странице редактирования меток."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:536
msgid "Slug Field Description"
msgstr "Описание поля слага"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:522
#: includes/admin/views/acf-taxonomy/advanced-settings.php:523
msgid "The name is how it appears on your site"
msgstr "Название определяет, как элемент будет отображаться на вашем сайте"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:521
msgid "Describes the Name field on the Edit Tags screen."
msgstr "Описывает поле «Название» на странице редактирования меток."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:520
msgid "Name Field Description"
msgstr "Описание имени слага"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:507
msgid "No tags"
msgstr "Меток нет"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:506
msgid ""
"Assigns the text displayed in the posts and media list tables when no tags "
"or categories are available."
msgstr ""
"Назначает текст, отображаемый в таблицах постов и списка медиафайлов при "
"отсутствии тегов и категорий."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:505
msgid "No Terms"
msgstr "Нет терминов"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:501
msgid "No %s"
msgstr "Нет %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:486
msgid "No tags found"
msgstr "Метки не найдены"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:485
msgid ""
"Assigns the text displayed when clicking the 'choose from most used' text in "
"the taxonomy meta box when no tags are available, and assigns the text used "
"in the terms list table when there are no items for a taxonomy."
msgstr ""
"Назначает текст, отображаемый при нажатии на кнопку «выбрать из часто "
"используемых» в метабоксе таксономии, если метки отсутствуют, и назначает "
"текст, используемый в таблице списка терминов, если для таксономии нет "
"элементов."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:484
msgid "Not Found"
msgstr "Не найдено"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:463
msgid "Assigns text to the Title field of the Most Used tab."
msgstr "Назначает текст поля \"Заголовок\" вкладки \"Часто используемые\"."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:462
#: includes/admin/views/acf-taxonomy/advanced-settings.php:464
#: includes/admin/views/acf-taxonomy/advanced-settings.php:465
msgid "Most Used"
msgstr "Часто используемое"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:444
msgid "Choose from the most used tags"
msgstr "Выбрать из часто используемых меток"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:443
msgid ""
"Assigns the 'choose from most used' text used in the meta box when "
"JavaScript is disabled. Only used on non-hierarchical taxonomies."
msgstr ""
"Назначает текст «выбрать из наиболее используемых», используемый в "
"метабоксе, когда JavaScript отключен. Используется только для "
"неиерархических таксономий."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:442
msgid "Choose From Most Used"
msgstr "Выберите из наиболее часто используемых"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:438
msgid "Choose from the most used %s"
msgstr "Выберите из наиболее часто используемых %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:418
msgid "Add or remove tags"
msgstr "Добавить или удалить метки"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:417
msgid ""
"Assigns the add or remove items text used in the meta box when JavaScript is "
"disabled. Only used on non-hierarchical taxonomies"
msgstr ""
"Назначает текст добавления или удаления элементов, используемый в метабоксе, "
"когда JavaScript отключен. Используется только для неиерархических таксономий"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:416
msgid "Add Or Remove Items"
msgstr "Добавить или удалить элементы"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:412
msgid "Add or remove %s"
msgstr "Добавить или удалить %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:392
msgid "Separate tags with commas"
msgstr "Метки разделяются запятыми"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:391
msgid ""
"Assigns the separate item with commas text used in the taxonomy meta box. "
"Only used on non-hierarchical taxonomies."
msgstr ""
"Назначает текст разделителя через запятую, используемый в метабоксе "
"таксономии. Используется только для неиерархических таксономий."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:390
msgid "Separate Items With Commas"
msgstr "Разделять элементы запятыми"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:386
msgid "Separate %s with commas"
msgstr "Разделять %s запятыми"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:366
msgid "Popular Tags"
msgstr "Популярные метки"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:365
msgid "Assigns popular items text. Only used for non-hierarchical taxonomies."
msgstr "Назначает текст популярных элементов."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:364
msgid "Popular Items"
msgstr "Популярные элементы"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:361
msgid "Popular %s"
msgstr "Популярные %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:347
msgid "Search Tags"
msgstr "Поиск меток"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:346
msgid "Assigns search items text."
msgstr "Назначает текст элементов поиска."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:323
msgid "Parent Category:"
msgstr "Родительская рубрика:"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:322
msgid "Assigns parent item text, but with a colon (:) added to the end."
msgstr "Назначает текст родительского элемента, но с двоеточием (:) на конце."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:321
msgid "Parent Item With Colon"
msgstr "Родительский элемент через двоеточие"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:298
msgid "Parent Category"
msgstr "Родительская рубрика"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:297
msgid "Assigns parent item text. Only used on hierarchical taxonomies."
msgstr ""
"Назначает текст родительского элемента. Используется только для древовидных "
"таксономий."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:296
msgid "Parent Item"
msgstr "Родительский элемент"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:293
msgid "Parent %s"
msgstr "Родитель %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:278
msgid "New Tag Name"
msgstr "Название новой метки"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:277
msgid "Assigns the new item name text."
msgstr "Присваивает новый текст названия элемента."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:276
msgid "New Item Name"
msgstr "Название нового элемента"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:273
msgid "New %s Name"
msgstr "Новое %s название"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:258
msgid "Add New Tag"
msgstr "Добавить новую метку"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:257
msgid "Assigns the add new item text."
msgstr "Назначает текст добавления элемента."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:238
msgid "Update Tag"
msgstr "Обновить метку"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:237
msgid "Assigns the update item text."
msgstr "Назначает текст обновления элемента."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:236
msgid "Update Item"
msgstr "Обновить элемент"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:233
msgid "Update %s"
msgstr "Обновить %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:218
msgid "View Tag"
msgstr "Просмотреть метку"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:217
msgid "In the admin bar to view term during editing."
msgstr "В панели администратора для просмотра термина при его редактировании."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:198
msgid "Edit Tag"
msgstr "Изменить метку"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:197
msgid "At the top of the editor screen when editing a term."
msgstr "Сверху страницы редактирования при изменении термина."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:178
msgid "All Tags"
msgstr "Все метки"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:177
msgid "Assigns the all items text."
msgstr "Назначает всем элементам текст."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:158
msgid "Assigns the menu name text."
msgstr "Назначает текст названия меню."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:157
msgid "Menu Label"
msgstr "Этикетка меню"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:131
msgid "Active taxonomies are enabled and registered with WordPress."
msgstr "Активные таксономии включены и регистрируются в WordPress."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:115
msgid "A descriptive summary of the taxonomy."
msgstr "Описание таксономии."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:95
msgid "A descriptive summary of the term."
msgstr "Описание термина."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:94
msgid "Term Description"
msgstr "Описание термина"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:76
msgid "Single word, no spaces. Underscores and dashes allowed."
msgstr "Одно слово, без пробелов. Подчеркивания и тире разрешены."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:75
msgid "Term Slug"
msgstr "Ярлык термина"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:56
msgid "The name of the default term."
msgstr "Название термина по умолчанию."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:55
msgid "Term Name"
msgstr "Название термина"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:41
msgid ""
"Create a term for the taxonomy that cannot be deleted. It will not be "
"selected for posts by default."
msgstr ""
"Создайте термин для таксономии, который не может быть удален. По умолчанию "
"он не будет выбран для записей."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:40
msgid "Default Term"
msgstr "Термин по умолчанию"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:28
msgid ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to `wp_set_object_terms()`."
msgstr ""
"Должны ли термины в этой таксономии сортироваться в том порядке, в котором "
"они представлены в `wp_set_object_terms()`."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:27
msgid "Sort Terms"
msgstr "Сортировать термины"

#: includes/admin/views/acf-post-type/list-empty.php:14
msgid "Add Post Type"
msgstr "Добавить тип записи"

#: includes/admin/views/acf-post-type/list-empty.php:13
msgid ""
"Expand the functionality of WordPress beyond standard posts and pages with "
"custom post types."
msgstr ""
"Расширьте функциональность WordPress за пределы стандартных записей и "
"страниц с помощью пользовательских типов постов."

#: includes/admin/views/acf-post-type/list-empty.php:12
msgid "Add Your First Post Type"
msgstr "Добавьте свой первый тип записи"

#: includes/admin/views/acf-post-type/basic-settings.php:136
#: includes/admin/views/acf-taxonomy/basic-settings.php:135
msgid "I know what I'm doing, show me all the options."
msgstr "Я знаю, что делаю, покажи мне все варианты."

#: includes/admin/views/acf-post-type/basic-settings.php:135
#: includes/admin/views/acf-taxonomy/basic-settings.php:134
msgid "Advanced Configuration"
msgstr "Расширенная конфигурация"

#: includes/admin/views/acf-post-type/basic-settings.php:123
msgid "Hierarchical post types can have descendants (like pages)."
msgstr "Иерархические типы записей могут иметь потомков (например, страницы)."

#: includes/admin/views/acf-post-type/basic-settings.php:122
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1000
#: includes/admin/views/acf-taxonomy/basic-settings.php:121
msgid "Hierarchical"
msgstr "Иерархическая"

#: includes/admin/views/acf-post-type/basic-settings.php:107
msgid "Visible on the frontend and in the admin dashboard."
msgstr "Отображается на сайте и в панели администратора."

#: includes/admin/views/acf-post-type/basic-settings.php:106
#: includes/admin/views/acf-taxonomy/basic-settings.php:106
msgid "Public"
msgstr "Открыто"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:59
msgid "movie"
msgstr "фильм"

#: includes/admin/views/acf-post-type/basic-settings.php:57
msgid "Lower case letters, underscores and dashes only, Max 20 characters."
msgstr ""
"Только буквы нижнего регистра, подчеркивания и дефисы. Максимум 20 символов."

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:41
msgid "Movie"
msgstr "Фильм"

#: includes/admin/views/acf-post-type/basic-settings.php:39
#: includes/admin/views/acf-taxonomy/basic-settings.php:40
msgid "Singular Label"
msgstr "Одиночная этикетка"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:24
msgid "Movies"
msgstr "Фильмы"

#: includes/admin/views/acf-post-type/basic-settings.php:22
#: includes/admin/views/acf-taxonomy/basic-settings.php:23
msgid "Plural Label"
msgstr "Подпись множественного числа"

#: includes/admin/views/acf-post-type/advanced-settings.php:1313
msgid ""
"Optional custom controller to use instead of `WP_REST_Posts_Controller`."
msgstr ""
"Опциональный произвольный контроллер, который будет использоваться вместо "
"`WP_REST_Posts_Controller`."

#: includes/admin/views/acf-post-type/advanced-settings.php:1312
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1230
msgid "Controller Class"
msgstr "Класс контроллера"

#: includes/admin/views/acf-post-type/advanced-settings.php:1294
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1211
msgid "The namespace part of the REST API URL."
msgstr "Часть пространства имен в URL-адресе REST API."

#: includes/admin/views/acf-post-type/advanced-settings.php:1293
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1210
msgid "Namespace Route"
msgstr "Пространство имен маршрута"

#: includes/admin/views/acf-post-type/advanced-settings.php:1275
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1192
msgid "The base URL for the post type REST API URLs."
msgstr "Базовый URL-адрес для URL-адресов REST API типа записей."

#: includes/admin/views/acf-post-type/advanced-settings.php:1274
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1191
msgid "Base URL"
msgstr "Базовый URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1260
msgid ""
"Exposes this post type in the REST API. Required to use the block editor."
msgstr ""
"Передает этот тип записей в REST API. Требуется для использования редактора "
"блоков."

#: includes/admin/views/acf-post-type/advanced-settings.php:1259
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1174
msgid "Show In REST API"
msgstr "Показывать в REST API"

#: includes/admin/views/acf-post-type/advanced-settings.php:1238
msgid "Customize the query variable name."
msgstr "Настройте имя переменной запроса."

#: includes/admin/views/acf-post-type/advanced-settings.php:1237
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1074
msgid "Query Variable"
msgstr "Переменная запроса"

#: includes/admin/views/acf-post-type/advanced-settings.php:1215
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1052
msgid "No Query Variable Support"
msgstr "Нет поддержки переменных запросов"

#: includes/admin/views/acf-post-type/advanced-settings.php:1214
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1051
msgid "Custom Query Variable"
msgstr "Пользовательская переменная запроса"

#: includes/admin/views/acf-post-type/advanced-settings.php:1211
msgid ""
"Items can be accessed using the non-pretty permalink, eg. {post_type}"
"={post_slug}."
msgstr ""
"Доступ к элементам можно получить, используя некрасивую постоянную ссылку, "
"например {post_type}={post_slug}."

#: includes/admin/views/acf-post-type/advanced-settings.php:1210
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1047
msgid "Query Variable Support"
msgstr "Поддержка переменных запроса"

#: includes/admin/views/acf-post-type/advanced-settings.php:1185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1023
msgid "URLs for an item and items can be accessed with a query string."
msgstr ""
"Доступ к URL-адресам элемента и элементов можно получить с помощью строки "
"запроса."

#: includes/admin/views/acf-post-type/advanced-settings.php:1184
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1022
msgid "Publicly Queryable"
msgstr "Публично запрашиваемый"

#: includes/admin/views/acf-post-type/advanced-settings.php:1163
msgid "Custom slug for the Archive URL."
msgstr "Пользовательский ярлык для URL-адреса архива."

#: includes/admin/views/acf-post-type/advanced-settings.php:1162
msgid "Archive Slug"
msgstr "Ярлык архива"

#: includes/admin/views/acf-post-type/advanced-settings.php:1149
msgid ""
"Has an item archive that can be customized with an archive template file in "
"your theme."
msgstr ""
"Имеет архив записей, который может быть настроен с помощью файла шаблона "
"архива вашей темы."

#: includes/admin/views/acf-post-type/advanced-settings.php:1148
msgid "Archive"
msgstr "Архив"

#: includes/admin/views/acf-post-type/advanced-settings.php:1128
msgid "Pagination support for the items URLs such as the archives."
msgstr "Поддержка пагинации для URL элементов, таких как архивы."

#: includes/admin/views/acf-post-type/advanced-settings.php:1127
msgid "Pagination"
msgstr "Разделение на страницы"

#: includes/admin/views/acf-post-type/advanced-settings.php:1110
msgid "RSS feed URL for the post type items."
msgstr "URL-адрес RSS-канала для элементов типа записей."

#: includes/admin/views/acf-post-type/advanced-settings.php:1109
msgid "Feed URL"
msgstr "URL фида"

#: includes/admin/views/acf-post-type/advanced-settings.php:1091
#: includes/admin/views/acf-taxonomy/advanced-settings.php:981
msgid ""
"Alters the permalink structure to add the `WP_Rewrite::$front` prefix to "
"URLs."
msgstr ""
"Изменяет структуру постоянных ссылок для добавления префикса `WP_Rewrite::"
"$front` к URL-адресам."

#: includes/admin/views/acf-post-type/advanced-settings.php:1090
#: includes/admin/views/acf-taxonomy/advanced-settings.php:980
msgid "Front URL Prefix"
msgstr "Префикс URL-адреса"

#: includes/admin/views/acf-post-type/advanced-settings.php:1071
msgid "Customize the slug used in the URL."
msgstr "Настройте ярлык, используемый в URL."

#: includes/admin/views/acf-post-type/advanced-settings.php:1070
#: includes/admin/views/acf-taxonomy/advanced-settings.php:960
msgid "URL Slug"
msgstr "Ярлык URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1054
msgid "Permalinks for this post type are disabled."
msgstr "Постоянные ссылки для этого типа записей отключены."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1053
#: includes/admin/views/acf-taxonomy/advanced-settings.php:943
msgid ""
"Rewrite the URL using a custom slug defined in the input below. Your "
"permalink structure will be"
msgstr ""
"Перепишите URL, используя пользовательский ярлык, заданный в поле ниже. Ваша "
"структура постоянных ссылок будет выглядеть следующим образом"

#: includes/admin/views/acf-post-type/advanced-settings.php:1045
#: includes/admin/views/acf-taxonomy/advanced-settings.php:935
msgid "No Permalink (prevent URL rewriting)"
msgstr "Без постоянный ссылки (предотвращает перезапись URL)"

#: includes/admin/views/acf-post-type/advanced-settings.php:1044
#: includes/admin/views/acf-taxonomy/advanced-settings.php:934
msgid "Custom Permalink"
msgstr "Произвольная постоянная ссылка"

#: includes/admin/views/acf-post-type/advanced-settings.php:1043
#: includes/admin/views/acf-post-type/advanced-settings.php:1213
#: includes/admin/views/acf-post-type/basic-settings.php:56
msgid "Post Type Key"
msgstr "Ключ типа записи"

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1041
#: includes/admin/views/acf-post-type/advanced-settings.php:1051
msgid ""
"Rewrite the URL using the post type key as the slug. Your permalink "
"structure will be"
msgstr ""
"Перезаписывать URL, используя ключ типа записей в качестве ярлыка. Структура "
"вашей постоянной ссылки будет"

#: includes/admin/views/acf-post-type/advanced-settings.php:1039
#: includes/admin/views/acf-taxonomy/advanced-settings.php:930
msgid "Permalink Rewrite"
msgstr "Переписать постоянную ссылку"

#: includes/admin/views/acf-post-type/advanced-settings.php:1025
msgid "Delete items by a user when that user is deleted."
msgstr ""
"Удаление элементов, созданных пользователем, при удалении этого пользователя."

#: includes/admin/views/acf-post-type/advanced-settings.php:1024
msgid "Delete With User"
msgstr "Удалить вместе с пользователем"

#: includes/admin/views/acf-post-type/advanced-settings.php:1010
msgid "Allow the post type to be exported from 'Tools' > 'Export'."
msgstr "Разрешить экспорт типа сообщения из «Инструменты»> «Экспорт»."

#: includes/admin/views/acf-post-type/advanced-settings.php:1009
msgid "Can Export"
msgstr "Можно экспортировать"

#: includes/admin/views/acf-post-type/advanced-settings.php:978
msgid "Optionally provide a plural to be used in capabilities."
msgstr ""
"При необходимости укажите множественное число, которое будет использоваться "
"в правах доступа."

#: includes/admin/views/acf-post-type/advanced-settings.php:977
msgid "Plural Capability Name"
msgstr "Название прав доступа во множественном числе"

#: includes/admin/views/acf-post-type/advanced-settings.php:959
msgid "Choose another post type to base the capabilities for this post type."
msgstr ""
"Выберите другой тип записи, чтобы использовать возможности этого типа записи."

#: includes/admin/views/acf-post-type/advanced-settings.php:958
msgid "Singular Capability Name"
msgstr "Название прав доступа в единственном числе"

#: includes/admin/views/acf-post-type/advanced-settings.php:944
msgid ""
"By default the capabilities of the post type will inherit the 'Post' "
"capability names, eg. edit_post, delete_posts. Enable to use post type "
"specific capabilities, eg. edit_{singular}, delete_{plural}."
msgstr ""
"По умолчанию права доступа типа записей наследуют названия прав доступа у "
"\"Запись\", например, edit_post, delete_posts. Включите, чтобы использовать "
"характерные названия для типа записей, например edit_{ед_число}, "
"delete_{множ_число}."

#: includes/admin/views/acf-post-type/advanced-settings.php:943
msgid "Rename Capabilities"
msgstr "Переименование возможностей"

#: includes/admin/views/acf-post-type/advanced-settings.php:928
msgid "Exclude From Search"
msgstr "Исключить из поиска"

#: includes/admin/views/acf-post-type/advanced-settings.php:915
#: includes/admin/views/acf-taxonomy/advanced-settings.php:874
msgid ""
"Allow items to be added to menus in the 'Appearance' > 'Menus' screen. Must "
"be turned on in 'Screen options'."
msgstr ""
"Разрешить добавлять элементы в меню «Внешний вид» > «Меню». Должно быть "
"включено в «Настройках экрана»."

#: includes/admin/views/acf-post-type/advanced-settings.php:914
#: includes/admin/views/acf-taxonomy/advanced-settings.php:873
msgid "Appearance Menus Support"
msgstr "Поддержка меню внешнего вида"

#: includes/admin/views/acf-post-type/advanced-settings.php:896
msgid "Appears as an item in the 'New' menu in the admin bar."
msgstr "Появится как пункт в меню 'Добавить' на панели администратора."

#: includes/admin/views/acf-post-type/advanced-settings.php:895
msgid "Show In Admin Bar"
msgstr "Показать на панели админа"

#: includes/admin/views/acf-post-type/advanced-settings.php:861
msgid "Custom Meta Box Callback"
msgstr "Пользовательская колбэк-функция метабокса"

#: includes/admin/views/acf-post-type/advanced-settings.php:822
#: includes/fields/class-acf-field-icon_picker.php:611
msgid "Menu Icon"
msgstr "Значок меню"

#: includes/admin/views/acf-post-type/advanced-settings.php:778
msgid "The position in the sidebar menu in the admin dashboard."
msgstr "Позиция в меню боковой панели в панели администратора."

#: includes/admin/views/acf-post-type/advanced-settings.php:777
msgid "Menu Position"
msgstr "Позиция меню"

#: includes/admin/views/acf-post-type/advanced-settings.php:759
msgid ""
"By default the post type will get a new top level item in the admin menu. If "
"an existing top level item is supplied here, the post type will be added as "
"a submenu item under it."
msgstr ""
"По умолчанию тип записи является пунктом админ-меню верхнего уровня. Если "
"указать здесь существующий пункт верхнего уровня, то тип записи будет "
"добавлен в качестве его подпункта."

#: includes/admin/views/acf-post-type/advanced-settings.php:758
msgid "Admin Menu Parent"
msgstr "Родительское меню администратора"

#: includes/admin/views/acf-post-type/advanced-settings.php:739
#: includes/admin/views/acf-taxonomy/advanced-settings.php:734
msgid "Admin editor navigation in the sidebar menu."
msgstr "Навигация по редактору администратора в боковом меню."

#: includes/admin/views/acf-post-type/advanced-settings.php:738
#: includes/admin/views/acf-taxonomy/advanced-settings.php:733
msgid "Show In Admin Menu"
msgstr "Показывать в меню админа"

#: includes/admin/views/acf-post-type/advanced-settings.php:725
#: includes/admin/views/acf-taxonomy/advanced-settings.php:719
msgid "Items can be edited and managed in the admin dashboard."
msgstr "Управлять элементами и изменять их можно в консоли администратора."

#: includes/admin/views/acf-post-type/advanced-settings.php:724
#: includes/admin/views/acf-taxonomy/advanced-settings.php:718
msgid "Show In UI"
msgstr "Показывать в интерфейсе"

#: includes/admin/views/acf-post-type/advanced-settings.php:694
msgid "A link to a post."
msgstr "Ссылка на запись."

#: includes/admin/views/acf-post-type/advanced-settings.php:693
msgid "Description for a navigation link block variation."
msgstr "Описание для вариации блока навигационных ссылок."

#: includes/admin/views/acf-post-type/advanced-settings.php:692
#: includes/admin/views/acf-taxonomy/advanced-settings.php:702
msgid "Item Link Description"
msgstr "Описание ссылки на элемент"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:688
msgid "A link to a %s."
msgstr "Ссылка на %s."

#: includes/admin/views/acf-post-type/advanced-settings.php:673
msgid "Post Link"
msgstr "Ссылка записи"

#: includes/admin/views/acf-post-type/advanced-settings.php:672
msgid "Title for a navigation link block variation."
msgstr "Заголовок для вариации блока навигационных ссылок."

#: includes/admin/views/acf-post-type/advanced-settings.php:671
#: includes/admin/views/acf-taxonomy/advanced-settings.php:681
msgid "Item Link"
msgstr "Ссылка элемента"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:668
#: includes/admin/views/acf-taxonomy/advanced-settings.php:678
msgid "%s Link"
msgstr "Cсылка на %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:653
msgid "Post updated."
msgstr "Запись обновлена."

#: includes/admin/views/acf-post-type/advanced-settings.php:652
msgid "In the editor notice after an item is updated."
msgstr "В уведомлении редактора после обновления элемента."

#: includes/admin/views/acf-post-type/advanced-settings.php:651
msgid "Item Updated"
msgstr "Элемент обновлен"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:648
msgid "%s updated."
msgstr "%s обновлён."

#: includes/admin/views/acf-post-type/advanced-settings.php:633
msgid "Post scheduled."
msgstr "Запись запланирована к публикации."

#: includes/admin/views/acf-post-type/advanced-settings.php:632
msgid "In the editor notice after scheduling an item."
msgstr "В уведомлении редактора после планирования публикации элемента."

#: includes/admin/views/acf-post-type/advanced-settings.php:631
msgid "Item Scheduled"
msgstr "Элемент запланирован"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:628
msgid "%s scheduled."
msgstr "%s запланировано."

#: includes/admin/views/acf-post-type/advanced-settings.php:613
msgid "Post reverted to draft."
msgstr "Запись возвращена в черновики."

#: includes/admin/views/acf-post-type/advanced-settings.php:612
msgid "In the editor notice after reverting an item to draft."
msgstr "В уведомлении редактора после возврата элемента в черновики."

#: includes/admin/views/acf-post-type/advanced-settings.php:611
msgid "Item Reverted To Draft"
msgstr "Элемент возвращён к черновику"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:608
msgid "%s reverted to draft."
msgstr "%s преобразован в черновик."

#: includes/admin/views/acf-post-type/advanced-settings.php:593
msgid "Post published privately."
msgstr "Запись опубликована как личная."

#: includes/admin/views/acf-post-type/advanced-settings.php:592
msgid "In the editor notice after publishing a private item."
msgstr "В уведомлении редактора после публикации личного элемента."

#: includes/admin/views/acf-post-type/advanced-settings.php:591
msgid "Item Published Privately"
msgstr "Элемент опубликован приватно"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:588
msgid "%s published privately."
msgstr "%s опубликована приватно."

#: includes/admin/views/acf-post-type/advanced-settings.php:573
msgid "Post published."
msgstr "Запись опубликована."

#: includes/admin/views/acf-post-type/advanced-settings.php:572
msgid "In the editor notice after publishing an item."
msgstr "В уведомлении редактора после публикации элемента."

#: includes/admin/views/acf-post-type/advanced-settings.php:571
msgid "Item Published"
msgstr "Элемент опубликован"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:568
msgid "%s published."
msgstr "%s опубликовано."

#: includes/admin/views/acf-post-type/advanced-settings.php:553
msgid "Posts list"
msgstr "Список записей"

#: includes/admin/views/acf-post-type/advanced-settings.php:552
msgid "Used by screen readers for the items list on the post type list screen."
msgstr ""
"Используется скринридерами для списка элементов на странице списка типа "
"записей."

#: includes/admin/views/acf-post-type/advanced-settings.php:551
#: includes/admin/views/acf-taxonomy/advanced-settings.php:640
msgid "Items List"
msgstr "Список элементов"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:548
#: includes/admin/views/acf-taxonomy/advanced-settings.php:637
msgid "%s list"
msgstr "%s список"

#: includes/admin/views/acf-post-type/advanced-settings.php:533
msgid "Posts list navigation"
msgstr "Навигация по списку записей"

#: includes/admin/views/acf-post-type/advanced-settings.php:532
msgid ""
"Used by screen readers for the filter list pagination on the post type list "
"screen."
msgstr ""
"Используется скринридерами для заголовка фильтра пагинации на странице "
"списка типа записей."

#: includes/admin/views/acf-post-type/advanced-settings.php:531
#: includes/admin/views/acf-taxonomy/advanced-settings.php:620
msgid "Items List Navigation"
msgstr "Навигация по списку элементов"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:528
#: includes/admin/views/acf-taxonomy/advanced-settings.php:617
msgid "%s list navigation"
msgstr "%s навигация по списку"

#: includes/admin/views/acf-post-type/advanced-settings.php:512
msgid "Filter posts by date"
msgstr "Фильтровать записи по дате"

#: includes/admin/views/acf-post-type/advanced-settings.php:511
msgid ""
"Used by screen readers for the filter by date heading on the post type list "
"screen."
msgstr ""
"Используется скринридерами для заголовка фильтра даты на странице списка "
"типа записей."

#: includes/admin/views/acf-post-type/advanced-settings.php:510
msgid "Filter Items By Date"
msgstr "Фильтровать элементы по дате"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:506
msgid "Filter %s by date"
msgstr "Фильтр %s по дате"

#: includes/admin/views/acf-post-type/advanced-settings.php:491
msgid "Filter posts list"
msgstr "Фильтровать список записей"

#: includes/admin/views/acf-post-type/advanced-settings.php:490
msgid ""
"Used by screen readers for the filter links heading on the post type list "
"screen."
msgstr ""
"Используется скринридерами для фильтра заголовков ссылок на экране списка "
"типа записей."

#: includes/admin/views/acf-post-type/advanced-settings.php:489
msgid "Filter Items List"
msgstr "Фильтр списка элементов"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:485
msgid "Filter %s list"
msgstr "Фильтровать список %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:469
msgid "In the media modal showing all media uploaded to this item."
msgstr ""
"В модальном окне медиафайлов отображает все файлы, загруженные для этого "
"элемента."

#: includes/admin/views/acf-post-type/advanced-settings.php:468
msgid "Uploaded To This Item"
msgstr "Загружено в этот элемент"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:464
msgid "Uploaded to this %s"
msgstr "Загружено в это %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:449
msgid "Insert into post"
msgstr "Вставить в запись"

#: includes/admin/views/acf-post-type/advanced-settings.php:448
msgid "As the button label when adding media to content."
msgstr "В качестве текста кнопки для добавлении медиафайлов в содержание."

#: includes/admin/views/acf-post-type/advanced-settings.php:447
msgid "Insert Into Media Button"
msgstr "Кнопка вставки медиа"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:443
msgid "Insert into %s"
msgstr "Вставить в %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:428
msgid "Use as featured image"
msgstr "Использовать как изображение записи"

#: includes/admin/views/acf-post-type/advanced-settings.php:427
msgid ""
"As the button label for selecting to use an image as the featured image."
msgstr ""
"В качестве текста кнопки для установки изображения в роли изображения записи."

#: includes/admin/views/acf-post-type/advanced-settings.php:426
msgid "Use Featured Image"
msgstr "Использовать изображение записи"

#: includes/admin/views/acf-post-type/advanced-settings.php:413
msgid "Remove featured image"
msgstr "Удалить изображение записи"

#: includes/admin/views/acf-post-type/advanced-settings.php:412
msgid "As the button label when removing the featured image."
msgstr "В качестве текста кнопки для удаления изображения записи."

#: includes/admin/views/acf-post-type/advanced-settings.php:411
msgid "Remove Featured Image"
msgstr "Удалить изображение записи"

#: includes/admin/views/acf-post-type/advanced-settings.php:398
msgid "Set featured image"
msgstr "Задать изображение"

#: includes/admin/views/acf-post-type/advanced-settings.php:397
msgid "As the button label when setting the featured image."
msgstr "В качестве текста кнопки для установки изображения записи."

#: includes/admin/views/acf-post-type/advanced-settings.php:396
msgid "Set Featured Image"
msgstr "Задать изображение записи"

#: includes/admin/views/acf-post-type/advanced-settings.php:383
msgid "Featured image"
msgstr "Изображение записи"

#: includes/admin/views/acf-post-type/advanced-settings.php:382
msgid "In the editor used for the title of the featured image meta box."
msgstr "Используется для заголовка метабокса изображения записи в редакторе."

#: includes/admin/views/acf-post-type/advanced-settings.php:381
msgid "Featured Image Meta Box"
msgstr "Метабокс изображения записи"

#: includes/admin/views/acf-post-type/advanced-settings.php:368
msgid "Post Attributes"
msgstr "Свойства записи"

#: includes/admin/views/acf-post-type/advanced-settings.php:367
msgid "In the editor used for the title of the post attributes meta box."
msgstr "Используется для заголовка метабокса свойств записи в редакторе."

#: includes/admin/views/acf-post-type/advanced-settings.php:366
msgid "Attributes Meta Box"
msgstr "Метабокс свойств"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:363
msgid "%s Attributes"
msgstr "Атрибуты %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:348
msgid "Post Archives"
msgstr "Архивы записей"

#: includes/admin/views/acf-post-type/advanced-settings.php:347
msgid ""
"Adds 'Post Type Archive' items with this label to the list of posts shown "
"when adding items to an existing menu in a CPT with archives enabled. Only "
"appears when editing menus in 'Live Preview' mode and a custom archive slug "
"has been provided."
msgstr ""
"Добавляет элементы с меткой 'Архив элементов' в список записей, отображаемый "
"при добавлении элементов в существующее меню в CPT с включенными архивами. "
"Появляется только при редактировании меню в режиме 'Live Preview', если был "
"задан пользовательский ярлык архива."

#: includes/admin/views/acf-post-type/advanced-settings.php:346
msgid "Archives Nav Menu"
msgstr "Навигационное меню архивов"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:343
msgid "%s Archives"
msgstr "Архивы %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:328
msgid "No posts found in Trash"
msgstr "Записей в корзине не найдено"

#: includes/admin/views/acf-post-type/advanced-settings.php:327
msgid ""
"At the top of the post type list screen when there are no posts in the trash."
msgstr ""
"В верхней части экрана списка типа записей, когда нет записей в корзине."

#: includes/admin/views/acf-post-type/advanced-settings.php:326
msgid "No Items Found in Trash"
msgstr "Элементы не найдены в корзине"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:322
msgid "No %s found in Trash"
msgstr "В корзине не найдено %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:307
msgid "No posts found"
msgstr "Записей не найдено"

#: includes/admin/views/acf-post-type/advanced-settings.php:306
msgid ""
"At the top of the post type list screen when there are no posts to display."
msgstr ""
"В верхней части экрана списка типа записей, когда нет записей для "
"отображения."

#: includes/admin/views/acf-post-type/advanced-settings.php:305
msgid "No Items Found"
msgstr "Элементов не найдено"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:301
#: includes/admin/views/acf-taxonomy/advanced-settings.php:480
msgid "No %s found"
msgstr "Не найдено %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:286
msgid "Search Posts"
msgstr "Поиск записей"

#: includes/admin/views/acf-post-type/advanced-settings.php:285
msgid "At the top of the items screen when searching for an item."
msgstr "В верхней части экрана элементов при поиске элемента."

#: includes/admin/views/acf-post-type/advanced-settings.php:284
#: includes/admin/views/acf-taxonomy/advanced-settings.php:345
msgid "Search Items"
msgstr "Поиск элементов"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:281
#: includes/admin/views/acf-taxonomy/advanced-settings.php:342
msgid "Search %s"
msgstr "Поиск %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:266
msgid "Parent Page:"
msgstr "Родительская страница:"

#: includes/admin/views/acf-post-type/advanced-settings.php:265
msgid "For hierarchical types in the post type list screen."
msgstr "Для древовидных типов на экране со списком типов записей."

#: includes/admin/views/acf-post-type/advanced-settings.php:264
msgid "Parent Item Prefix"
msgstr "Префикс родительского элемента"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:261
#: includes/admin/views/acf-taxonomy/advanced-settings.php:318
msgid "Parent %s:"
msgstr "Родитель %s:"

#: includes/admin/views/acf-post-type/advanced-settings.php:246
msgid "New Post"
msgstr "Новая запись"

#: includes/admin/views/acf-post-type/advanced-settings.php:244
msgid "New Item"
msgstr "Новый элемент"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:241
msgid "New %s"
msgstr "Новый %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:206
#: includes/admin/views/acf-post-type/advanced-settings.php:226
msgid "Add New Post"
msgstr "Добавить запись"

#: includes/admin/views/acf-post-type/advanced-settings.php:205
msgid "At the top of the editor screen when adding a new item."
msgstr "В верхней части экрана редактора при добавлении нового элемента."

#: includes/admin/views/acf-post-type/advanced-settings.php:204
#: includes/admin/views/acf-taxonomy/advanced-settings.php:256
msgid "Add New Item"
msgstr "Добавить новый элемент"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:201
#: includes/admin/views/acf-post-type/advanced-settings.php:221
#: includes/admin/views/acf-taxonomy/advanced-settings.php:253
msgid "Add New %s"
msgstr "Добавить новое %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:186
msgid "View Posts"
msgstr "Просмотр записей"

#: includes/admin/views/acf-post-type/advanced-settings.php:185
msgid ""
"Appears in the admin bar in the 'All Posts' view, provided the post type "
"supports archives and the home page is not an archive of that post type."
msgstr ""
"Отображается в панели администратора в представлении «Все записи», если тип "
"записи поддерживает архивы и главная страница не является архивом этого типа "
"записи."

#: includes/admin/views/acf-post-type/advanced-settings.php:184
msgid "View Items"
msgstr "Просмотр элементов"

#: includes/admin/views/acf-post-type/advanced-settings.php:166
msgid "View Post"
msgstr "Просмотреть запись"

#: includes/admin/views/acf-post-type/advanced-settings.php:165
msgid "In the admin bar to view item when editing it."
msgstr "В панели администратора для просмотра элемента при его редактировании."

#: includes/admin/views/acf-post-type/advanced-settings.php:164
#: includes/admin/views/acf-taxonomy/advanced-settings.php:216
msgid "View Item"
msgstr "Просмотреть элемент"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:161
#: includes/admin/views/acf-post-type/advanced-settings.php:181
#: includes/admin/views/acf-taxonomy/advanced-settings.php:213
msgid "View %s"
msgstr "Посмотреть %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:146
msgid "Edit Post"
msgstr "Редактировать запись"

#: includes/admin/views/acf-post-type/advanced-settings.php:145
msgid "At the top of the editor screen when editing an item."
msgstr "В верхней части экрана редактора при редактировании элемента."

#: includes/admin/views/acf-post-type/advanced-settings.php:144
#: includes/admin/views/acf-taxonomy/advanced-settings.php:196
msgid "Edit Item"
msgstr "Изменить элемент"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:141
#: includes/admin/views/acf-taxonomy/advanced-settings.php:193
msgid "Edit %s"
msgstr "Изменить %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:126
msgid "All Posts"
msgstr "Все записи"

#: includes/admin/views/acf-post-type/advanced-settings.php:125
#: includes/admin/views/acf-post-type/advanced-settings.php:225
#: includes/admin/views/acf-post-type/advanced-settings.php:245
msgid "In the post type submenu in the admin dashboard."
msgstr "В подменю типа записи на административной консоли."

#: includes/admin/views/acf-post-type/advanced-settings.php:124
#: includes/admin/views/acf-taxonomy/advanced-settings.php:176
msgid "All Items"
msgstr "Все элементы"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:121
#: includes/admin/views/acf-taxonomy/advanced-settings.php:173
msgid "All %s"
msgstr "Все %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:105
msgid "Admin menu name for the post type."
msgstr "Название в админ-меню для типа записей."

#: includes/admin/views/acf-post-type/advanced-settings.php:104
msgid "Menu Name"
msgstr "Название меню"

#: includes/admin/views/acf-post-type/advanced-settings.php:90
#: includes/admin/views/acf-taxonomy/advanced-settings.php:142
msgid "Regenerate all labels using the Singular and Plural labels"
msgstr ""
"Пересоздать все метки, используя метки единственного и множественного числа"

#: includes/admin/views/acf-post-type/advanced-settings.php:88
#: includes/admin/views/acf-taxonomy/advanced-settings.php:140
msgid "Regenerate"
msgstr "Регенерировать"

#: includes/admin/views/acf-post-type/advanced-settings.php:79
msgid "Active post types are enabled and registered with WordPress."
msgstr "Активные типы записей включены и регистрируются в WordPress."

#: includes/admin/views/acf-post-type/advanced-settings.php:63
msgid "A descriptive summary of the post type."
msgstr "Описательная сводка типа поста."

#: includes/admin/views/acf-post-type/advanced-settings.php:48
msgid "Add Custom"
msgstr "Добавить пользовательский"

#: includes/admin/views/acf-post-type/advanced-settings.php:42
msgid "Enable various features in the content editor."
msgstr "Включить различные функции в редакторе содержимого."

#: includes/admin/views/acf-post-type/advanced-settings.php:31
msgid "Post Formats"
msgstr "Форматы записей"

#: includes/admin/views/acf-post-type/advanced-settings.php:25
msgid "Editor"
msgstr "Редактор"

#: includes/admin/views/acf-post-type/advanced-settings.php:24
msgid "Trackbacks"
msgstr "Обратные ссылки"

#: includes/admin/views/acf-post-type/basic-settings.php:87
msgid "Select existing taxonomies to classify items of the post type."
msgstr ""
"Выберите существующие таксономии, чтобы классифицировать элементы типа "
"записи."

#: includes/admin/views/acf-field-group/field.php:158
msgid "Browse Fields"
msgstr "Выбрать поле"

#: includes/admin/tools/class-acf-admin-tool-import.php:287
msgid "Nothing to import"
msgstr "Импортировать нечего"

#: includes/admin/tools/class-acf-admin-tool-import.php:282
msgid ". The Custom Post Type UI plugin can be deactivated."
msgstr ". Плагин Custom Post Type UI можно деактивировать."

#. translators: %d - number of items imported from CPTUI
#: includes/admin/tools/class-acf-admin-tool-import.php:273
msgid "Imported %d item from Custom Post Type UI -"
msgid_plural "Imported %d items from Custom Post Type UI -"
msgstr[0] "Импортировано %d запись из Custom Post Type UI -"
msgstr[1] "Импортировано %d записи из Custom Post Type UI -"
msgstr[2] "Импортировано %d записей из Custom Post Type UI -"

#: includes/admin/tools/class-acf-admin-tool-import.php:257
msgid "Failed to import taxonomies."
msgstr "Не удалось импортировать таксономии."

#: includes/admin/tools/class-acf-admin-tool-import.php:239
msgid "Failed to import post types."
msgstr "Не удалось импортировать типы записи."

#: includes/admin/tools/class-acf-admin-tool-import.php:228
msgid "Nothing from Custom Post Type UI plugin selected for import."
msgstr "Ничего из плагина Custom Post Type UI не выбрано для импорта."

#: includes/admin/tools/class-acf-admin-tool-import.php:204
msgid "Imported 1 item"
msgid_plural "Imported %s items"
msgstr[0] "Импортирован 1 элемент"
msgstr[1] "Импортировано %s элемента"
msgstr[2] "Импортировано %s элементов"

#: includes/admin/tools/class-acf-admin-tool-import.php:119
msgid ""
"Importing a Post Type or Taxonomy with the same key as one that already "
"exists will overwrite the settings for the existing Post Type or Taxonomy "
"with those of the import."
msgstr ""
"Импорт типа записей или таксономии с уже существующим ключом приведет к "
"перезаписи параметров существующего типа записей или таксономии параметрами "
"из импорта."

#: includes/admin/tools/class-acf-admin-tool-import.php:108
#: includes/admin/tools/class-acf-admin-tool-import.php:124
msgid "Import from Custom Post Type UI"
msgstr "Импорт из Custom Post Type UI"

#: includes/admin/tools/class-acf-admin-tool-export.php:354
msgid ""
"The following code can be used to register a local version of the selected "
"items. Storing field groups, post types, or taxonomies locally can provide "
"many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's "
"functions.php file or include it within an external file, then deactivate or "
"delete the items from the ACF admin."
msgstr ""
"Следующий код можно использовать для регистрации локальной версии выбранных "
"элементов. Локальное хранение групп полей, типов записей или таксономий дает "
"множество преимуществ, таких как ускоренная загрузка, контроль версий и "
"динамические поля/настройки. Просто скопируйте и вставьте следующий код в "
"файл functions.php вашей темы или включите его во внешний файл, а затем "
"деактивируйте или удалите элементы из админки ACF."

#: includes/admin/tools/class-acf-admin-tool-export.php:353
msgid "Export - Generate PHP"
msgstr "Экспорт - Генерация PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:330
msgid "Export"
msgstr "Экспорт"

#: includes/admin/tools/class-acf-admin-tool-export.php:264
msgid "Select Taxonomies"
msgstr "Выбрать таксономии"

#: includes/admin/tools/class-acf-admin-tool-export.php:239
msgid "Select Post Types"
msgstr "Выбрать типы записи"

#: includes/admin/tools/class-acf-admin-tool-export.php:155
msgid "Exported 1 item."
msgid_plural "Exported %s items."
msgstr[0] "Экспортирован 1 элемент."
msgstr[1] "Экспортировано %s элемента."
msgstr[2] "Экспортировано %s элементов."

#: includes/admin/post-types/admin-taxonomy.php:127
msgid "Category"
msgstr "Рубрика"

#: includes/admin/post-types/admin-taxonomy.php:125
msgid "Tag"
msgstr "Метка"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:82
msgid "%s taxonomy created"
msgstr "Таксономия %s создана"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:76
msgid "%s taxonomy updated"
msgstr "Таксономия %s обновлена"

#: includes/admin/post-types/admin-taxonomy.php:56
msgid "Taxonomy draft updated."
msgstr "Черновик таксономии обновлен."

#: includes/admin/post-types/admin-taxonomy.php:55
msgid "Taxonomy scheduled for."
msgstr "Таксономия запланирована на."

#: includes/admin/post-types/admin-taxonomy.php:54
msgid "Taxonomy submitted."
msgstr "Таксономия отправлена."

#: includes/admin/post-types/admin-taxonomy.php:53
msgid "Taxonomy saved."
msgstr "Таксономия сохранена."

#: includes/admin/post-types/admin-taxonomy.php:49
msgid "Taxonomy deleted."
msgstr "Таксономия удалена."

#: includes/admin/post-types/admin-taxonomy.php:48
msgid "Taxonomy updated."
msgstr "Таксономия обновлена."

#: includes/admin/post-types/admin-taxonomies.php:351
#: includes/admin/post-types/admin-taxonomy.php:153
msgid ""
"This taxonomy could not be registered because its key is in use by another "
"taxonomy registered by another plugin or theme."
msgstr ""
"Эта таксономия не может быть зарегистрирована, так как ее ключ используется "
"другой таксономией, зарегистрированной другим плагином или темой."

#. translators: %s number of taxonomies synchronized
#: includes/admin/post-types/admin-taxonomies.php:333
msgid "Taxonomy synchronized."
msgid_plural "%s taxonomies synchronized."
msgstr[0] "Таксономия синхронизирована"
msgstr[1] "%s таксономии синхронизированы"
msgstr[2] "%s таксономий синхронизировано"

#. translators: %s number of taxonomies duplicated
#: includes/admin/post-types/admin-taxonomies.php:326
msgid "Taxonomy duplicated."
msgid_plural "%s taxonomies duplicated."
msgstr[0] "Таксономия дублирована"
msgstr[1] "%s таксономии дублированы"
msgstr[2] "%s таксономий дублировано"

#. translators: %s number of taxonomies deactivated
#: includes/admin/post-types/admin-taxonomies.php:319
msgid "Taxonomy deactivated."
msgid_plural "%s taxonomies deactivated."
msgstr[0] "Таксономия деактивирована"
msgstr[1] "%s таксономии деактивированы"
msgstr[2] "%s таксономий деактивировано"

#. translators: %s number of taxonomies activated
#: includes/admin/post-types/admin-taxonomies.php:312
msgid "Taxonomy activated."
msgid_plural "%s taxonomies activated."
msgstr[0] "Таксономия активирована"
msgstr[1] "%s таксономии активированы"
msgstr[2] "%s таксономий активировано"

#: includes/admin/post-types/admin-taxonomies.php:113
msgid "Terms"
msgstr "Термины"

#. translators: %s number of post types synchronized
#: includes/admin/post-types/admin-post-types.php:327
msgid "Post type synchronized."
msgid_plural "%s post types synchronized."
msgstr[0] "Тип записей синхронизирован"
msgstr[1] "%s типа записей синхронизированы"
msgstr[2] "%s типов записей синхронизировано"

#. translators: %s number of post types duplicated
#: includes/admin/post-types/admin-post-types.php:320
msgid "Post type duplicated."
msgid_plural "%s post types duplicated."
msgstr[0] "Тип записей дублирован"
msgstr[1] "%s типа записей дублированы"
msgstr[2] "%s типов записей дублировано"

#. translators: %s number of post types deactivated
#: includes/admin/post-types/admin-post-types.php:313
msgid "Post type deactivated."
msgid_plural "%s post types deactivated."
msgstr[0] "Тип записей деактивирован"
msgstr[1] "%s типа записей деактивированы"
msgstr[2] "%s типов записей деактивировано"

#. translators: %s number of post types activated
#: includes/admin/post-types/admin-post-types.php:306
msgid "Post type activated."
msgid_plural "%s post types activated."
msgstr[0] "Тип записей активирован"
msgstr[1] "%s тип записей активированы"
msgstr[2] "%s типов записей активировано"

#: includes/admin/post-types/admin-post-types.php:87
#: includes/admin/post-types/admin-taxonomies.php:111
#: includes/admin/tools/class-acf-admin-tool-import.php:79
#: includes/admin/views/acf-taxonomy/basic-settings.php:82
#: includes/post-types/class-acf-post-type.php:91
msgid "Post Types"
msgstr "Типы записей"

#: includes/admin/post-types/admin-post-type.php:158
#: includes/admin/post-types/admin-taxonomy.php:160
msgid "Advanced Settings"
msgstr "Расширенные настройки"

#: includes/admin/post-types/admin-post-type.php:157
#: includes/admin/post-types/admin-taxonomy.php:159
msgid "Basic Settings"
msgstr "Базовые настройки"

#: includes/admin/post-types/admin-post-type.php:151
#: includes/admin/post-types/admin-post-types.php:345
msgid ""
"This post type could not be registered because its key is in use by another "
"post type registered by another plugin or theme."
msgstr ""
"Тип записей не может быть зарегистрирован, так как его ключ уже "
"зарегистрирован другим плагином или темой."

#: includes/admin/post-types/admin-post-type.php:126
msgid "Pages"
msgstr "Страницы"

#: includes/admin/admin-internal-post-type.php:347
msgid "Link Existing Field Groups"
msgstr "Связать существующие группы полей"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:80
msgid "%s post type created"
msgstr "Тип записи %s создан"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:78
msgid "Add fields to %s"
msgstr "Добавить поля в %s"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:76
msgid "%s post type updated"
msgstr "Тип записи %s обновлен"

#: includes/admin/post-types/admin-post-type.php:56
msgid "Post type draft updated."
msgstr "Черновик типа записей обновлен."

#: includes/admin/post-types/admin-post-type.php:55
msgid "Post type scheduled for."
msgstr "Тип записей запланирован к публикации."

#: includes/admin/post-types/admin-post-type.php:54
msgid "Post type submitted."
msgstr "Тип записей отправлен."

#: includes/admin/post-types/admin-post-type.php:53
msgid "Post type saved."
msgstr "Тип записи сохранён."

#: includes/admin/post-types/admin-post-type.php:50
msgid "Post type updated."
msgstr "Тип записей обновлен."

#: includes/admin/post-types/admin-post-type.php:49
msgid "Post type deleted."
msgstr "Тип записей удален."

#: includes/admin/post-types/admin-field-group.php:146
msgid "Type to search..."
msgstr "Введите текст для поиска..."

#: includes/admin/post-types/admin-field-group.php:101
msgid "PRO Only"
msgstr "Только для Про"

#: includes/admin/post-types/admin-field-group.php:93
msgid "Field groups linked successfully."
msgstr "Группы полей связаны успешно."

#. translators: %s - URL to ACF tools page.
#: includes/admin/admin.php:199
msgid ""
"Import Post Types and Taxonomies registered with Custom Post Type UI and "
"manage them with ACF. <a href=\"%s\">Get Started</a>."
msgstr ""
"Импортировать типы записей и таксономии, зарегистрированные через Custom "
"Post Type UI, и управлять ими с помощью ACF. <a href=\"%s\">Начать</a>."

#: includes/admin/admin.php:46 includes/admin/admin.php:361
#: src/Site_Health/Site_Health.php:254
msgid "ACF"
msgstr "ACF"

#: includes/admin/admin-internal-post-type.php:314
msgid "taxonomy"
msgstr "таксономия"

#: includes/admin/admin-internal-post-type.php:314
msgid "post type"
msgstr "тип записи"

#: includes/admin/admin-internal-post-type.php:338
msgid "Done"
msgstr "Готово"

#: includes/admin/admin-internal-post-type.php:324
msgid "Field Group(s)"
msgstr "Группа(ы) полей"

#: includes/admin/admin-internal-post-type.php:323
msgid "Select one or many field groups..."
msgstr "Выберите одну или несколько групп полей..."

#: includes/admin/admin-internal-post-type.php:322
msgid "Please select the field groups to link."
msgstr "Пожалуйста, выберете группы полей, чтобы связать."

#: includes/admin/admin-internal-post-type.php:280
msgid "Field group linked successfully."
msgid_plural "Field groups linked successfully."
msgstr[0] "Группа полей связана успешно."
msgstr[1] "Группы полей связаны успешно."
msgstr[2] "Группы полей связаны успешно."

#: includes/admin/admin-internal-post-type-list.php:277
#: includes/admin/post-types/admin-post-types.php:346
#: includes/admin/post-types/admin-taxonomies.php:352
msgctxt "post status"
msgid "Registration Failed"
msgstr "Регистрация не удалась"

#: includes/admin/admin-internal-post-type-list.php:276
msgid ""
"This item could not be registered because its key is in use by another item "
"registered by another plugin or theme."
msgstr ""
"Этот элемент не может быть зарегистрирован, так как его ключ используется "
"другим элементом, зарегистрированным другим плагином или темой."

#: includes/acf-internal-post-type-functions.php:509
#: includes/acf-internal-post-type-functions.php:538
msgid "REST API"
msgstr "Rest API"

#: includes/acf-internal-post-type-functions.php:508
#: includes/acf-internal-post-type-functions.php:537
#: includes/acf-internal-post-type-functions.php:564
msgid "Permissions"
msgstr "Разрешения"

#: includes/acf-internal-post-type-functions.php:507
#: includes/acf-internal-post-type-functions.php:536
msgid "URLs"
msgstr "URL-адреса"

#: includes/acf-internal-post-type-functions.php:506
#: includes/acf-internal-post-type-functions.php:535
#: includes/acf-internal-post-type-functions.php:562
msgid "Visibility"
msgstr "Видимость"

#: includes/acf-internal-post-type-functions.php:505
#: includes/acf-internal-post-type-functions.php:534
#: includes/acf-internal-post-type-functions.php:563
msgid "Labels"
msgstr "Этикетки"

#: includes/admin/post-types/admin-field-group.php:279
msgid "Field Settings Tabs"
msgstr "Вкладки настроек полей"

#. Author URI of the plugin
#: acf.php
msgid ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"
msgstr ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"

#: includes/api/api-template.php:1027
msgid "[ACF shortcode value disabled for preview]"
msgstr "[Значение шорткода ACF отключено для предварительного просмотра]"

#: includes/admin/admin-internal-post-type.php:290
#: includes/admin/post-types/admin-field-group.php:572
msgid "Close Modal"
msgstr "Закрыть модальное окно"

#: includes/admin/post-types/admin-field-group.php:92
msgid "Field moved to other group"
msgstr "Поле перемещено в другую группу"

#: includes/admin/post-types/admin-field-group.php:91
msgid "Close modal"
msgstr "Закрыть модальное окно"

#: includes/fields/class-acf-field-tab.php:122
msgid "Start a new group of tabs at this tab."
msgstr "Начать новую группу вкладок с этой вкладки."

#: includes/fields/class-acf-field-tab.php:121
msgid "New Tab Group"
msgstr "Новая группа вкладок"

#: includes/fields/class-acf-field-select.php:421
#: includes/fields/class-acf-field-true_false.php:188
msgid "Use a stylized checkbox using select2"
msgstr "Используйте стилизованный флажок, используя select2"

#: includes/fields/class-acf-field-radio.php:250
msgid "Save Other Choice"
msgstr "Сохранить другой выбор"

#: includes/fields/class-acf-field-radio.php:239
msgid "Allow Other Choice"
msgstr "Разрешить другой выбор"

#: includes/fields/class-acf-field-checkbox.php:420
msgid "Add Toggle All"
msgstr "Добавить Переключить все"

#: includes/fields/class-acf-field-checkbox.php:379
msgid "Save Custom Values"
msgstr "Сохранить пользовательские значения"

#: includes/fields/class-acf-field-checkbox.php:368
msgid "Allow Custom Values"
msgstr "Разрешить пользовательские значения"

#: includes/fields/class-acf-field-checkbox.php:134
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""
"Пользовательские значения флажка не могут быть пустыми. Снимите флажки с "
"пустых значений."

#: includes/admin/views/global/navigation.php:253
msgid "Updates"
msgstr "Обновления"

#: includes/admin/views/global/navigation.php:177
#: includes/admin/views/global/navigation.php:181
msgid "Advanced Custom Fields logo"
msgstr "Логотип дополнительных настраиваемых полей"

#: includes/admin/views/global/form-top.php:92
msgid "Save Changes"
msgstr "Сохранить изменения"

#: includes/admin/views/global/form-top.php:79
msgid "Field Group Title"
msgstr "Название группы полей"

#: includes/admin/views/acf-post-type/advanced-settings.php:709
#: includes/admin/views/global/form-top.php:3
msgid "Add title"
msgstr "Добавить заголовок"

#. translators: %s url to getting started guide
#: includes/admin/views/acf-field-group/list-empty.php:30
#: includes/admin/views/acf-post-type/list-empty.php:20
#: includes/admin/views/acf-taxonomy/list-empty.php:21
#: includes/admin/views/options-page-preview.php:13
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""
"Вы впервые в ACF? Ознакомьтесь с нашим <a href=\"%s\" "
"target=\"_blank\">руководством по началу работы</a>."

#: includes/admin/views/acf-field-group/list-empty.php:24
msgid "Add Field Group"
msgstr "Добавить группу полей"

#. translators: %s url to creating a field group page
#: includes/admin/views/acf-field-group/list-empty.php:18
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""
"ACF использует <a href=\"%s\" target=\"_blank\">группы полей</a> для "
"группировки произвольных полей вместе, а затем присоединяет эти поля к "
"экранным формам редактирования."

#: includes/admin/views/acf-field-group/list-empty.php:12
msgid "Add Your First Field Group"
msgstr "Добавьте первую группу полей"

#: includes/admin/admin-options-pages-preview.php:28
#: includes/admin/views/acf-field-group/pro-features.php:58
#: includes/admin/views/global/navigation.php:86
#: includes/admin/views/global/navigation.php:255
msgid "Options Pages"
msgstr "Страницы настроек"

#: includes/admin/views/acf-field-group/pro-features.php:54
msgid "ACF Blocks"
msgstr "Блоки ACF"

#: includes/admin/views/acf-field-group/pro-features.php:62
msgid "Gallery Field"
msgstr "Поле галереи"

#: includes/admin/views/acf-field-group/pro-features.php:42
msgid "Flexible Content Field"
msgstr "Гибкое поле содержимого"

#: includes/admin/views/acf-field-group/pro-features.php:46
msgid "Repeater Field"
msgstr "Повторяющееся поле"

#: includes/admin/views/global/navigation.php:215
msgid "Unlock Extra Features with ACF PRO"
msgstr "Разблокируйте дополнительные возможности с помощью ACF PRO"

#: includes/admin/views/acf-field-group/options.php:267
msgid "Delete Field Group"
msgstr "Удалить группу полей"

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/acf-field-group/options.php:261
msgid "Created on %1$s at %2$s"
msgstr "Создано %1$s в %2$s"

#: includes/acf-field-group-functions.php:497
msgid "Group Settings"
msgstr "Настройки группы"

#: includes/acf-field-group-functions.php:495
msgid "Location Rules"
msgstr "Правила местонахождения"

#. translators: %s url to field types list
#: includes/admin/views/acf-field-group/fields.php:73
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""
"Выбирайте из более чем 30 типов полей. <a href=\"%s\" "
"target=\"_blank\">Подробнее</a>."

#: includes/admin/views/acf-field-group/fields.php:65
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""
"Начните создавать новые пользовательские поля для ваших записей, страниц, "
"пользовательских типов записей и другого содержимого WordPress."

#: includes/admin/views/acf-field-group/fields.php:64
msgid "Add Your First Field"
msgstr "Добавить первое поле"

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/acf-field-group/fields.php:43
msgid "#"
msgstr "#"

#: includes/admin/views/acf-field-group/fields.php:33
#: includes/admin/views/acf-field-group/fields.php:67
#: includes/admin/views/acf-field-group/fields.php:101
#: includes/admin/views/global/form-top.php:88
msgid "Add Field"
msgstr "Добавить поле"

#: includes/acf-field-group-functions.php:496 includes/fields.php:384
msgid "Presentation"
msgstr "Презентация"

#: includes/fields.php:383
msgid "Validation"
msgstr "Валидация"

#: includes/acf-internal-post-type-functions.php:504
#: includes/acf-internal-post-type-functions.php:533 includes/fields.php:382
msgid "General"
msgstr "Общие"

#: includes/admin/tools/class-acf-admin-tool-import.php:67
msgid "Import JSON"
msgstr "Импорт JSON"

#: includes/admin/tools/class-acf-admin-tool-export.php:338
msgid "Export As JSON"
msgstr "Экспорт в формате JSON"

#. translators: %s number of field groups deactivated
#: includes/admin/post-types/admin-field-groups.php:360
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] "%s группа полей деактивирована."
msgstr[1] "%s группы полей деактивировано."
msgstr[2] "%s групп полей деактивировано."

#. translators: %s number of field groups activated
#: includes/admin/post-types/admin-field-groups.php:353
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] "%s группа полей активирована."
msgstr[1] "%s группы полей активировано."
msgstr[2] "%s групп полей активировано."

#: includes/admin/admin-internal-post-type-list.php:470
#: includes/admin/admin-internal-post-type-list.php:496
msgid "Deactivate"
msgstr "Деактивировать"

#: includes/admin/admin-internal-post-type-list.php:470
msgid "Deactivate this item"
msgstr "Деактивировать этот элемент"

#: includes/admin/admin-internal-post-type-list.php:466
#: includes/admin/admin-internal-post-type-list.php:495
msgid "Activate"
msgstr "Активировать"

#: includes/admin/admin-internal-post-type-list.php:466
msgid "Activate this item"
msgstr "Активировать этот элемент"

#: includes/admin/post-types/admin-field-group.php:88
msgid "Move field group to trash?"
msgstr "Переместить группу полей в корзину?"

#: acf.php:519 includes/admin/admin-internal-post-type-list.php:264
#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Inactive"
msgstr "Неактивна"

#. Author of the plugin
#: acf.php
msgid "WP Engine"
msgstr "WP Engine"

#: acf.php:577
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""
"Продвинутые пользовательские поля и Продвинутые пользовательские поля PRO не "
"должны быть активны одновременно. Мы автоматически деактивировали "
"Продвинутые пользовательские поля PRO."

#: acf.php:575
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""
"Продвинутые пользовательские поля и Продвинутые пользовательские поля PRO не "
"должны быть активны одновременно. Мы автоматически деактивировали "
"Продвинутые пользовательские поля."

#: includes/fields/class-acf-field-user.php:578
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] "%1$s должно содержать пользователя со следующими ролью: %2$s"
msgstr[1] "%1$s должно содержать пользователя со следующими ролями: %2$s"
msgstr[2] "%1$s должно содержать пользователя со следующими ролями: %2$s"

#: includes/fields/class-acf-field-user.php:569
msgid "%1$s must have a valid user ID."
msgstr "%1$s должен иметь действительный ID пользователя."

#: includes/fields/class-acf-field-user.php:408
msgid "Invalid request."
msgstr "Неверный запрос."

#: includes/fields/class-acf-field-select.php:635
msgid "%1$s is not one of %2$s"
msgstr "%1$s не является одним из %2$s"

#: includes/fields/class-acf-field-post_object.php:660
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/fields/class-acf-field-post_object.php:644
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/fields/class-acf-field-post_object.php:635
msgid "%1$s must have a valid post ID."
msgstr "%1$s должен иметь действительный ID записи."

#: includes/fields/class-acf-field-file.php:447
msgid "%s requires a valid attachment ID."
msgstr "%s требуется действительный ID вложения."

#: includes/admin/views/acf-field-group/options.php:233
msgid "Show in REST API"
msgstr "Показывать в REST API"

#: includes/fields/class-acf-field-color_picker.php:156
msgid "Enable Transparency"
msgstr "Включить прозрачность"

#: includes/fields/class-acf-field-color_picker.php:175
msgid "RGBA Array"
msgstr "Массив RGBA"

#: includes/fields/class-acf-field-color_picker.php:92
msgid "RGBA String"
msgstr "Строка RGBA"

#: includes/fields/class-acf-field-color_picker.php:91
#: includes/fields/class-acf-field-color_picker.php:174
msgid "Hex String"
msgstr "Cтрока hex"

#: includes/admin/views/browse-fields-modal.php:12
msgid "Upgrade to PRO"
msgstr "Обновить до PRO"

#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Active"
msgstr "Активен"

#: includes/fields/class-acf-field-email.php:166
msgid "'%s' is not a valid email address"
msgstr "«%s» не является корректным адресом электропочты"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Color value"
msgstr "Значение цвета"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Select default color"
msgstr "Выбрать стандартный цвет"

#: includes/fields/class-acf-field-color_picker.php:66
msgid "Clear color"
msgstr "Очистить цвет"

#: includes/acf-wp-functions.php:90
msgid "Blocks"
msgstr "Блоки"

#: includes/acf-wp-functions.php:86
msgid "Options"
msgstr "Настройки"

#: includes/acf-wp-functions.php:82
msgid "Users"
msgstr "Пользователи"

#: includes/acf-wp-functions.php:78
msgid "Menu items"
msgstr "Пункты меню"

#: includes/acf-wp-functions.php:70
msgid "Widgets"
msgstr "Виджеты"

#: includes/acf-wp-functions.php:62
msgid "Attachments"
msgstr "Вложения"

#: includes/acf-wp-functions.php:57
#: includes/admin/post-types/admin-post-types.php:112
#: includes/admin/post-types/admin-taxonomies.php:86
#: includes/admin/tools/class-acf-admin-tool-import.php:90
#: includes/admin/views/acf-post-type/basic-settings.php:86
#: includes/post-types/class-acf-taxonomy.php:90
#: includes/post-types/class-acf-taxonomy.php:91
msgid "Taxonomies"
msgstr "Таксономии"

#: includes/acf-wp-functions.php:44
#: includes/admin/post-types/admin-post-type.php:124
#: includes/admin/post-types/admin-post-types.php:114
#: includes/admin/views/acf-post-type/advanced-settings.php:106
msgid "Posts"
msgstr "Записи"

#: includes/ajax/class-acf-ajax-local-json-diff.php:81
msgid "Last updated: %s"
msgstr "Последнее изменение: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:75
msgid "Sorry, this post is unavailable for diff comparison."
msgstr "Данная группа полей не доступна для сравнения отличий."

#: includes/ajax/class-acf-ajax-local-json-diff.php:47
msgid "Invalid field group parameter(s)."
msgstr "Неверный параметр(ы) группы полей."

#: includes/admin/admin-internal-post-type-list.php:429
msgid "Awaiting save"
msgstr "Ожидает сохранения"

#: includes/admin/admin-internal-post-type-list.php:426
msgid "Saved"
msgstr "Сохранено"

#: includes/admin/admin-internal-post-type-list.php:422
#: includes/admin/tools/class-acf-admin-tool-import.php:46
msgid "Import"
msgstr "Импорт"

#: includes/admin/admin-internal-post-type-list.php:418
msgid "Review changes"
msgstr "Просмотр изменений"

#: includes/admin/admin-internal-post-type-list.php:394
msgid "Located in: %s"
msgstr "Находится в: %s"

#: includes/admin/admin-internal-post-type-list.php:391
msgid "Located in plugin: %s"
msgstr "Находится в плагине: %s"

#: includes/admin/admin-internal-post-type-list.php:388
msgid "Located in theme: %s"
msgstr "Находится в теме: %s"

#: includes/admin/post-types/admin-field-groups.php:235
msgid "Various"
msgstr "Различные"

#: includes/admin/admin-internal-post-type-list.php:230
#: includes/admin/admin-internal-post-type-list.php:503
msgid "Sync changes"
msgstr "Синхронизировать изменения"

#: includes/admin/admin-internal-post-type-list.php:229
msgid "Loading diff"
msgstr "Загрузка diff"

#: includes/admin/admin-internal-post-type-list.php:228
msgid "Review local JSON changes"
msgstr "Обзор локальных изменений JSON"

#: includes/admin/admin.php:174
msgid "Visit website"
msgstr "Перейти на сайт"

#: includes/admin/admin.php:173
msgid "View details"
msgstr "Подробности"

#: includes/admin/admin.php:172
msgid "Version %s"
msgstr "Версия %s"

#: includes/admin/admin.php:171
msgid "Information"
msgstr "Информация"

#: includes/admin/admin.php:162
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Служба поддержки</a>. Специалисты нашей "
"службы поддержки помогут решить ваши технические проблемы."

#: includes/admin/admin.php:158
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the 'how-tos' of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Обсуждения</a>. У нас есть активное и "
"дружелюбное сообщество на наших форумах сообщества, которое может помочь вам "
"разобраться в практических приемах мира ACF."

#: includes/admin/admin.php:154
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Документация</a>. Наша подробная "
"документация содержит ссылки и руководства для большинства ситуаций, с "
"которыми вы можете столкнуться."

#: includes/admin/admin.php:151
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"Мы фанатично относимся к поддержке и хотим, чтобы вы извлекали максимум из "
"своего веб-сайта с помощью ACF. Если вы столкнетесь с какими-либо "
"трудностями, есть несколько мест, где вы можете найти помощь:"

#: includes/admin/admin.php:148 includes/admin/admin.php:150
msgid "Help & Support"
msgstr "Помощь и поддержка"

#: includes/admin/admin.php:139
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""
"Воспользуйтесь вкладкой «Справка и поддержка», чтобы связаться с нами, если "
"вам потребуется помощь."

#: includes/admin/admin.php:136
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"Перед созданием вашей первой группы полей мы рекомендуем сначала прочитать "
"наше <a href=\"%s\" target=\"_blank\">руководство по началу работы</a>, "
"чтобы ознакомиться с философией и передовыми практиками плагина."

#: includes/admin/admin.php:134
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"Плагин Advanced Custom Fields предоставляет визуальный конструктор форм для "
"настройки экранов редактирования WordPress с дополнительными полями и "
"интуитивно понятный API для отображения значений произвольных полей в любом "
"файле шаблона темы."

#: includes/admin/admin.php:131 includes/admin/admin.php:133
msgid "Overview"
msgstr "Обзор"

#. translators: %s the name of the location type
#: includes/locations.php:38
msgid "Location type \"%s\" is already registered."
msgstr "Тип местоположения \"%s\" уже зарегистрирован."

#. translators: %s class name for a location that could not be found
#: includes/locations.php:26
msgid "Class \"%s\" does not exist."
msgstr "Класса \"%s\" не существует."

#: includes/ajax/class-acf-ajax-query-users.php:43
#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "Неверный одноразовый номер."

#: includes/fields/class-acf-field-user.php:400
msgid "Error loading field."
msgstr "Ошибка загрузки поля."

#: includes/forms/form-user.php:328
msgid "<strong>Error</strong>: %s"
msgstr "<strong>Ошибка</strong>: %s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Виджет"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "Роль пользователя"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Комментарий"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Формат записи"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Элемент меню"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "Статус записи"

#: includes/acf-wp-functions.php:74
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Меню"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Области для меню"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Меню"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Таксономия записи"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Дочерняя страница (имеет родителя)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Родительская страница (имеет дочерние)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Страница верхнего уровня (без родителей)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Страница записей"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Главная страница"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Тип страницы"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "Просмотр админки"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "Просмотр фронтэнда"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Авторизован"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Текущий пользователь"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Шаблон страницы"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "Регистрация"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Добавить / изменить"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Форма пользователя"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Родительская страница"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Супер администратор"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Текущая роль пользователя"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Шаблон по умолчанию"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Шаблон записи"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Рубрика записи"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "Все %s форматы"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Вложение"

#: includes/validation.php:323
msgid "%s value is required"
msgstr "%s значение требуется"

#: includes/admin/views/acf-field-group/conditional-logic.php:64
msgid "Show this field if"
msgstr "Показывать это поле, если"

#: includes/admin/views/acf-field-group/conditional-logic.php:25
#: includes/admin/views/acf-field-group/field.php:122 includes/fields.php:385
msgid "Conditional Logic"
msgstr "Условная логика"

#: includes/admin/views/acf-field-group/conditional-logic.php:169
#: includes/admin/views/acf-field-group/location-rule.php:84
msgid "and"
msgstr "и"

#: includes/admin/post-types/admin-field-groups.php:97
#: includes/admin/post-types/admin-post-types.php:118
#: includes/admin/post-types/admin-taxonomies.php:117
msgid "Local JSON"
msgstr "Локальный JSON"

#: includes/admin/views/acf-field-group/pro-features.php:50
msgid "Clone Field"
msgstr "Клонировать поле"

#. translators: %s a list of plugin
#: includes/admin/views/upgrade/notice.php:32
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Также убедитесь, что все надстройки премиум-класса (%s) обновлены до "
"последней версии."

#: includes/admin/views/upgrade/notice.php:29
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr "Эта версия содержит улучшения вашей базы данных и требует обновления."

#. translators: %1 plugin name, %2 version number
#: includes/admin/views/upgrade/notice.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "Спасибо за обновление до %1$s v%2$s!"

#: includes/admin/views/upgrade/notice.php:26
msgid "Database Upgrade Required"
msgstr "Требуется обновление БД"

#: includes/admin/post-types/admin-field-group.php:159
#: includes/admin/views/upgrade/notice.php:17
msgid "Options Page"
msgstr "Страница настроек"

#: includes/admin/views/upgrade/notice.php:14 includes/fields.php:436
msgid "Gallery"
msgstr "Галерея"

#: includes/admin/views/upgrade/notice.php:11 includes/fields.php:426
msgid "Flexible Content"
msgstr "Гибкое содержимое"

#: includes/admin/views/upgrade/notice.php:8 includes/fields.php:446
msgid "Repeater"
msgstr "Повторитель"

#: includes/admin/views/tools/tools.php:16
msgid "Back to all tools"
msgstr "Вернуться ко всем инструментам"

#: includes/admin/views/acf-field-group/options.php:195
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Если на странице редактирования присутствует несколько групп полей, то будут "
"использованы настройки первой из них (с наиболее низким значением порядка "
"очередности)"

#: includes/admin/views/acf-field-group/options.php:195
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr ""
"<b>Выберите</b> блоки, которые необходимо <b>скрыть</b> на странице "
"редактирования."

#: includes/admin/views/acf-field-group/options.php:194
msgid "Hide on screen"
msgstr "Скрыть на экране"

#: includes/admin/views/acf-field-group/options.php:186
msgid "Send Trackbacks"
msgstr "Отправить обратные ссылки"

#: includes/admin/post-types/admin-taxonomy.php:126
#: includes/admin/views/acf-field-group/options.php:185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:159
msgid "Tags"
msgstr "Метки"

#: includes/admin/post-types/admin-taxonomy.php:128
#: includes/admin/views/acf-field-group/options.php:184
msgid "Categories"
msgstr "Рубрики"

#: includes/admin/views/acf-field-group/options.php:182
#: includes/admin/views/acf-post-type/advanced-settings.php:28
msgid "Page Attributes"
msgstr "Атрибуты страницы"

#: includes/admin/views/acf-field-group/options.php:181
msgid "Format"
msgstr "Формат"

#: includes/admin/views/acf-field-group/options.php:180
#: includes/admin/views/acf-post-type/advanced-settings.php:22
msgid "Author"
msgstr "Автор"

#: includes/admin/views/acf-field-group/options.php:179
msgid "Slug"
msgstr "Ярлык"

#: includes/admin/views/acf-field-group/options.php:178
#: includes/admin/views/acf-post-type/advanced-settings.php:27
msgid "Revisions"
msgstr "Редакции"

#: includes/acf-wp-functions.php:66
#: includes/admin/views/acf-field-group/options.php:177
#: includes/admin/views/acf-post-type/advanced-settings.php:23
msgid "Comments"
msgstr "Комментарии"

#: includes/admin/views/acf-field-group/options.php:176
msgid "Discussion"
msgstr "Обсуждение"

#: includes/admin/views/acf-field-group/options.php:174
#: includes/admin/views/acf-post-type/advanced-settings.php:26
msgid "Excerpt"
msgstr "Отрывок"

#: includes/admin/views/acf-field-group/options.php:173
msgid "Content Editor"
msgstr "Текстовый редактор"

#: includes/admin/views/acf-field-group/options.php:172
msgid "Permalink"
msgstr "Постоянная ссылка"

#: includes/admin/views/acf-field-group/options.php:250
msgid "Shown in field group list"
msgstr "Отображается в списке групп полей"

#: includes/admin/views/acf-field-group/options.php:157
msgid "Field groups with a lower order will appear first"
msgstr "Группа полей с самым низким порядковым номером появится первой"

#: includes/admin/views/acf-field-group/options.php:156
msgid "Order No."
msgstr "Порядковый номер"

#: includes/admin/views/acf-field-group/options.php:147
msgid "Below fields"
msgstr "Под полями"

#: includes/admin/views/acf-field-group/options.php:146
msgid "Below labels"
msgstr "Под метками"

#: includes/admin/views/acf-field-group/options.php:139
msgid "Instruction Placement"
msgstr "Размещение инструкции"

#: includes/admin/views/acf-field-group/options.php:122
msgid "Label Placement"
msgstr "Размещение этикетки"

#: includes/admin/views/acf-field-group/options.php:110
msgid "Side"
msgstr "На боковой панели"

#: includes/admin/views/acf-field-group/options.php:109
msgid "Normal (after content)"
msgstr "Обычный (после содержимого)"

#: includes/admin/views/acf-field-group/options.php:108
msgid "High (after title)"
msgstr "Высокий (после названия)"

#: includes/admin/views/acf-field-group/options.php:101
msgid "Position"
msgstr "Позиция"

#: includes/admin/views/acf-field-group/options.php:92
msgid "Seamless (no metabox)"
msgstr "Бесшовный (без метабокса)"

#: includes/admin/views/acf-field-group/options.php:91
msgid "Standard (WP metabox)"
msgstr "Стандарт (метабокс WP)"

#: includes/admin/views/acf-field-group/options.php:84
msgid "Style"
msgstr "Стиль"

#: includes/admin/views/acf-field-group/fields.php:55
msgid "Type"
msgstr "Тип"

#: includes/admin/post-types/admin-field-groups.php:91
#: includes/admin/post-types/admin-post-types.php:111
#: includes/admin/post-types/admin-taxonomies.php:110
#: includes/admin/views/acf-field-group/fields.php:54
msgid "Key"
msgstr "Ключ"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/acf-field-group/fields.php:48
msgid "Order"
msgstr "Порядок"

#: includes/admin/views/acf-field-group/field.php:321
msgid "Close Field"
msgstr "Закрыть поле"

#: includes/admin/views/acf-field-group/field.php:252
msgid "id"
msgstr "id"

#: includes/admin/views/acf-field-group/field.php:236
msgid "class"
msgstr "класс"

#: includes/admin/views/acf-field-group/field.php:278
msgid "width"
msgstr "ширина"

#: includes/admin/views/acf-field-group/field.php:272
msgid "Wrapper Attributes"
msgstr "Атрибуты обёртки"

#: includes/fields/class-acf-field.php:312
msgid "Required"
msgstr "Обязательное"

#: includes/admin/views/acf-field-group/field.php:219
msgid "Instructions"
msgstr "Инструкции"

#: includes/admin/views/acf-field-group/field.php:142
msgid "Field Type"
msgstr "Тип поля"

#: includes/admin/views/acf-field-group/field.php:183
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Одиночное слово, без пробелов. Подчеркивания и тире разрешены"

#: includes/admin/views/acf-field-group/field.php:182
msgid "Field Name"
msgstr "Символьный код"

#: includes/admin/views/acf-field-group/field.php:170
msgid "This is the name which will appear on the EDIT page"
msgstr "Имя поля на странице редактирования"

#: includes/admin/views/acf-field-group/field.php:169
#: includes/admin/views/browse-fields-modal.php:69
msgid "Field Label"
msgstr "Название поля"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete"
msgstr "Удалить"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete field"
msgstr "Удалить поле"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move"
msgstr "Переместить"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move field to another group"
msgstr "Переместить поле в другую группу"

#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate field"
msgstr "Дублировать поле"

#: includes/admin/views/acf-field-group/field.php:86
#: includes/admin/views/acf-field-group/field.php:89
msgid "Edit field"
msgstr "Изменить поле"

#: includes/admin/views/acf-field-group/field.php:82
msgid "Drag to reorder"
msgstr "Перетащите, чтобы изменить порядок"

#: includes/admin/post-types/admin-field-group.php:99
#: includes/admin/views/acf-field-group/location-group.php:3
msgid "Show this field group if"
msgstr "Показать эту группу полей, если"

#: includes/admin/views/upgrade/upgrade.php:93
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "Обновлений нет."

#. translators: %s the url to the field group page.
#: includes/admin/views/upgrade/upgrade.php:32
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr "Обновление БД завершено. <a href=\"%s\">Посмотрите, что нового</a>"

#: includes/admin/views/upgrade/upgrade.php:27
msgid "Reading upgrade tasks..."
msgstr "Чтения задач обновления..."

#: includes/admin/views/upgrade/network.php:165
#: includes/admin/views/upgrade/upgrade.php:64
msgid "Upgrade failed."
msgstr "Ошибка обновления."

#: includes/admin/views/upgrade/network.php:162
msgid "Upgrade complete."
msgstr "Обновление завершено."

#. translators: %s the version being upgraded to.
#. translators: %s the new ACF version
#: includes/admin/views/upgrade/network.php:148
#: includes/admin/views/upgrade/upgrade.php:29
msgid "Upgrading data to version %s"
msgstr "Обновление данных до версии %s"

#: includes/admin/views/upgrade/network.php:120
#: includes/admin/views/upgrade/notice.php:46
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Мы настоятельно рекомендуем сделать резервную копию базы данных перед "
"началом работы. Вы уверены, что хотите запустить обновление сейчас?"

#: includes/admin/views/upgrade/network.php:116
msgid "Please select at least one site to upgrade."
msgstr "Выберите хотя бы один сайт для обновления."

#. translators: %s admin dashboard url page
#: includes/admin/views/upgrade/network.php:96
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr "Обновление БД закончено. <a href=\"%s\">Вернуться в консоль</a>"

#: includes/admin/views/upgrade/network.php:79
msgid "Site is up to date"
msgstr "Сайт обновлен"

#. translators: %1 current db version, %2 available db version
#: includes/admin/views/upgrade/network.php:77
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr "Сайт требует обновления БД с %1$s до %2$s"

#: includes/admin/views/upgrade/network.php:34
#: includes/admin/views/upgrade/network.php:45
msgid "Site"
msgstr "Сайт"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
#: includes/admin/views/upgrade/network.php:25
#: includes/admin/views/upgrade/network.php:94
msgid "Upgrade Sites"
msgstr "Обновление сайтов"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Следующие сайты требуют обновления БД. Отметьте те, которые вы хотите "
"обновить, а затем нажмите %s."

#: includes/admin/views/acf-field-group/conditional-logic.php:184
#: includes/admin/views/acf-field-group/locations.php:37
msgid "Add rule group"
msgstr "Добавить группу правил"

#: includes/admin/views/acf-field-group/locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Создайте набор правил для указания страниц, где следует отображать группу "
"полей"

#: includes/admin/views/acf-field-group/locations.php:9
msgid "Rules"
msgstr "Правила"

#: includes/admin/tools/class-acf-admin-tool-export.php:449
msgid "Copied"
msgstr "Скопировано"

#: includes/admin/tools/class-acf-admin-tool-export.php:425
msgid "Copy to clipboard"
msgstr "Скопировать в буфер обмена"

#: includes/admin/tools/class-acf-admin-tool-export.php:331
msgid ""
"Select the items you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""
"Выберите элементы, которые вы хотите экспортировать, а затем выберите метод "
"экспорта. Экспортировать как JSON для экспорта в файл .json, который затем "
"можно импортировать в другую установку ACF. Сгенерировать PHP для экспорта в "
"PHP-код, который можно разместить в вашей теме."

#: includes/admin/tools/class-acf-admin-tool-export.php:215
msgid "Select Field Groups"
msgstr "Выберите группы полей"

#: includes/admin/tools/class-acf-admin-tool-export.php:88
#: includes/admin/tools/class-acf-admin-tool-export.php:121
msgid "No field groups selected"
msgstr "Не выбраны группы полей"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:339
#: includes/admin/tools/class-acf-admin-tool-export.php:363
msgid "Generate PHP"
msgstr "Генерировать PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:34
msgid "Export Field Groups"
msgstr "Экспорт групп полей"

#: includes/admin/tools/class-acf-admin-tool-import.php:172
msgid "Import file empty"
msgstr "Файл импорта пуст"

#: includes/admin/tools/class-acf-admin-tool-import.php:163
msgid "Incorrect file type"
msgstr "Неправильный тип файла"

#: includes/admin/tools/class-acf-admin-tool-import.php:158
msgid "Error uploading file. Please try again"
msgstr "Ошибка при загрузке файла. Попробуйте еще раз"

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the items in that file."
msgstr ""
"Выберите файл ACF JSON, который вы хотите импортировать. Когда вы нажмете "
"кнопку импорта ниже, ACF импортирует элементы из этого файла."

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "Импорт групп полей"

#: includes/admin/admin-internal-post-type-list.php:417
msgid "Sync"
msgstr "Синхронизация"

#. translators: %s: field group title
#: includes/admin/admin-internal-post-type-list.php:960
msgid "Select %s"
msgstr "Выбрать %s"

#: includes/admin/admin-internal-post-type-list.php:460
#: includes/admin/admin-internal-post-type-list.php:492
#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate"
msgstr "Дублировать"

#: includes/admin/admin-internal-post-type-list.php:460
msgid "Duplicate this item"
msgstr "Дублировать элемент"

#: includes/admin/views/acf-post-type/advanced-settings.php:41
msgid "Supports"
msgstr "Поддержка"

#: includes/admin/admin.php:355
#: includes/admin/views/browse-fields-modal.php:102
msgid "Documentation"
msgstr "Документация"

#: includes/admin/post-types/admin-field-groups.php:90
#: includes/admin/post-types/admin-post-types.php:110
#: includes/admin/post-types/admin-taxonomies.php:109
#: includes/admin/views/acf-field-group/options.php:249
#: includes/admin/views/acf-post-type/advanced-settings.php:62
#: includes/admin/views/acf-taxonomy/advanced-settings.php:114
#: includes/admin/views/upgrade/network.php:36
#: includes/admin/views/upgrade/network.php:47
msgid "Description"
msgstr "Описание"

#: includes/admin/admin-internal-post-type-list.php:414
#: includes/admin/admin-internal-post-type-list.php:832
msgid "Sync available"
msgstr "Доступна синхронизация"

#. translators: %s number of field groups synchronized
#: includes/admin/post-types/admin-field-groups.php:374
msgid "Field group synchronized."
msgid_plural "%s field groups synchronized."
msgstr[0] "Группа полей синхронизирована."
msgstr[1] "%s группы полей синхронизированы."
msgstr[2] "%s групп полей синхронизированы."

#. translators: %s number of field groups duplicated
#: includes/admin/post-types/admin-field-groups.php:367
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "% группа полей продублирована."
msgstr[1] "%s группы полей продублировано."
msgstr[2] "%s групп полей продублировано."

#: includes/admin/admin-internal-post-type-list.php:155
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Активна <span class=\"count\">(%s)</span>"
msgstr[1] "Активно <span class=\"count\">(%s)</span>"
msgstr[2] "Активны <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:251
msgid "Review sites & upgrade"
msgstr "Проверьте и обновите сайт"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:90
#: includes/admin/admin-upgrade.php:91 includes/admin/admin-upgrade.php:227
#: includes/admin/views/upgrade/network.php:21
#: includes/admin/views/upgrade/upgrade.php:23
msgid "Upgrade Database"
msgstr "Обновить базу данных"

#: includes/admin/views/acf-field-group/options.php:175
#: includes/admin/views/acf-post-type/advanced-settings.php:30
msgid "Custom Fields"
msgstr "Произвольные поля"

#: includes/admin/post-types/admin-field-group.php:609
msgid "Move Field"
msgstr "Переместить поле"

#: includes/admin/post-types/admin-field-group.php:602
#: includes/admin/post-types/admin-field-group.php:606
msgid "Please select the destination for this field"
msgstr "Выберите местоположение для этого поля"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/post-types/admin-field-group.php:568
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "Поле %1$s теперь можно найти в группе полей %2$s"

#: includes/admin/post-types/admin-field-group.php:565
msgid "Move Complete."
msgstr "Движение завершено."

#: includes/admin/views/acf-field-group/field.php:52
#: includes/admin/views/acf-field-group/options.php:217
#: includes/admin/views/acf-post-type/advanced-settings.php:78
#: includes/admin/views/acf-taxonomy/advanced-settings.php:130
msgid "Active"
msgstr "Активен"

#: includes/admin/post-types/admin-field-group.php:276
msgid "Field Keys"
msgstr "Ключи полей"

#: includes/admin/post-types/admin-field-group.php:180
msgid "Settings"
msgstr "Настройки"

#: includes/admin/post-types/admin-field-groups.php:92
msgid "Location"
msgstr "Местонахождение"

#: includes/admin/post-types/admin-field-group.php:100
msgid "Null"
msgstr "Null"

#: includes/admin/post-types/admin-field-group.php:97
#: includes/class-acf-internal-post-type.php:728
#: includes/post-types/class-acf-field-group.php:345
msgid "copy"
msgstr "копировать"

#: includes/admin/post-types/admin-field-group.php:96
msgid "(this field)"
msgstr "(текущее поле)"

#: includes/admin/post-types/admin-field-group.php:94
msgid "Checked"
msgstr "Выбрано"

#: includes/admin/post-types/admin-field-group.php:90
msgid "Move Custom Field"
msgstr "Переместить пользовательское поле"

#: includes/admin/post-types/admin-field-group.php:89
msgid "No toggle fields available"
msgstr "Нет доступных переключаемых полей"

#: includes/admin/post-types/admin-field-group.php:87
msgid "Field group title is required"
msgstr "Название группы полей обязательно"

#: includes/admin/post-types/admin-field-group.php:86
msgid "This field cannot be moved until its changes have been saved"
msgstr "Это поле не может быть перемещено до сохранения изменений"

#: includes/admin/post-types/admin-field-group.php:85
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "Строка \"field_\" не может использоваться в начале имени поля"

#: includes/admin/post-types/admin-field-group.php:69
msgid "Field group draft updated."
msgstr "Черновик группы полей обновлен."

#: includes/admin/post-types/admin-field-group.php:68
msgid "Field group scheduled for."
msgstr "Группа полей запланирована на."

#: includes/admin/post-types/admin-field-group.php:67
msgid "Field group submitted."
msgstr "Группа полей отправлена."

#: includes/admin/post-types/admin-field-group.php:66
msgid "Field group saved."
msgstr "Группа полей сохранена."

#: includes/admin/post-types/admin-field-group.php:65
msgid "Field group published."
msgstr "Группа полей опубликована."

#: includes/admin/post-types/admin-field-group.php:62
msgid "Field group deleted."
msgstr "Группа полей удалена."

#: includes/admin/post-types/admin-field-group.php:60
#: includes/admin/post-types/admin-field-group.php:61
#: includes/admin/post-types/admin-field-group.php:63
msgid "Field group updated."
msgstr "Группа полей обновлена."

#: includes/admin/admin-tools.php:107
#: includes/admin/views/global/navigation.php:251
#: includes/admin/views/tools/tools.php:13
msgid "Tools"
msgstr "Инструменты"

#: includes/locations/abstract-acf-location.php:105
msgid "is not equal to"
msgstr "не равно"

#: includes/locations/abstract-acf-location.php:104
msgid "is equal to"
msgstr "равно"

#: includes/locations.php:104
msgid "Forms"
msgstr "Формы"

#: includes/admin/post-types/admin-post-type.php:125 includes/locations.php:102
#: includes/locations/class-acf-location-page.php:22
msgid "Page"
msgstr "Страница"

#: includes/admin/post-types/admin-post-type.php:123 includes/locations.php:101
#: includes/locations/class-acf-location-post.php:22
msgid "Post"
msgstr "Запись"

#: includes/fields.php:328
msgid "Relational"
msgstr "Отношение"

#: includes/fields.php:327
msgid "Choice"
msgstr "Выбор"

#: includes/fields.php:325
msgid "Basic"
msgstr "Базовый"

#: includes/fields.php:276
msgid "Unknown"
msgstr "Неизвестный"

#: includes/fields.php:276
msgid "Field type does not exist"
msgstr "Тип поля не существует"

#: includes/forms/form-front.php:220
msgid "Spam Detected"
msgstr "Обнаружение спама"

#: includes/forms/form-front.php:103
msgid "Post updated"
msgstr "Запись обновлена"

#: includes/forms/form-front.php:102
msgid "Update"
msgstr "Обновить"

#: includes/forms/form-front.php:63
msgid "Validate Email"
msgstr "Проверка Email"

#: includes/fields.php:326 includes/forms/form-front.php:55
msgid "Content"
msgstr "Содержимое"

#: includes/admin/views/acf-post-type/advanced-settings.php:21
#: includes/forms/form-front.php:46
msgid "Title"
msgstr "Заголовок"

#: includes/assets.php:376 includes/forms/form-comment.php:140
msgid "Edit field group"
msgstr "Изменить группу полей"

#: includes/admin/post-types/admin-field-group.php:113
msgid "Selection is less than"
msgstr "Отбор меньше, чем"

#: includes/admin/post-types/admin-field-group.php:112
msgid "Selection is greater than"
msgstr "Отбор больше, чем"

#: includes/admin/post-types/admin-field-group.php:111
msgid "Value is less than"
msgstr "Значение меньше чем"

#: includes/admin/post-types/admin-field-group.php:110
msgid "Value is greater than"
msgstr "Значение больше чем"

#: includes/admin/post-types/admin-field-group.php:109
msgid "Value contains"
msgstr "Значение содержит"

#: includes/admin/post-types/admin-field-group.php:108
msgid "Value matches pattern"
msgstr "Значение соответствует паттерну"

#: includes/admin/post-types/admin-field-group.php:107
msgid "Value is not equal to"
msgstr "Значение не равно"

#: includes/admin/post-types/admin-field-group.php:106
msgid "Value is equal to"
msgstr "Значение равно"

#: includes/admin/post-types/admin-field-group.php:105
msgid "Has no value"
msgstr "Не имеет значения"

#: includes/admin/post-types/admin-field-group.php:104
msgid "Has any value"
msgstr "Имеет любое значение"

#: includes/admin/admin-internal-post-type.php:337
#: includes/admin/views/browse-fields-modal.php:72 includes/assets.php:354
msgid "Cancel"
msgstr "Отмена"

#: includes/assets.php:350
msgid "Are you sure?"
msgstr "Вы уверены?"

#: includes/assets.php:370
msgid "%d fields require attention"
msgstr "%d полей требуют вашего внимания"

#: includes/assets.php:369
msgid "1 field requires attention"
msgstr "1 поле требует внимания"

#: includes/assets.php:368 includes/validation.php:257
#: includes/validation.php:265
msgid "Validation failed"
msgstr "Валидация не удалась"

#: includes/assets.php:367
msgid "Validation successful"
msgstr "Валидация пройдена успешно"

#: includes/media.php:54
msgid "Restricted"
msgstr "Ограничено"

#: includes/media.php:53
msgid "Collapse Details"
msgstr "Свернуть подробные сведения"

#: includes/media.php:52
msgid "Expand Details"
msgstr "Развернуть подробные сведения"

#: includes/admin/views/acf-post-type/advanced-settings.php:470
#: includes/media.php:51
msgid "Uploaded to this post"
msgstr "Загруженные для этой записи"

#: includes/media.php:50
msgctxt "verb"
msgid "Update"
msgstr "Обновить"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Изменить"

#: includes/assets.php:364
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "Внесенные вами изменения будут утеряны, если вы покинете эту страницу"

#: includes/api/api-helpers.php:3000
msgid "File type must be %s."
msgstr "Тип файла должен быть %s."

#: includes/admin/post-types/admin-field-group.php:98
#: includes/admin/views/acf-field-group/conditional-logic.php:64
#: includes/admin/views/acf-field-group/conditional-logic.php:182
#: includes/admin/views/acf-field-group/location-group.php:3
#: includes/admin/views/acf-field-group/locations.php:35
#: includes/api/api-helpers.php:2997
msgid "or"
msgstr "или"

#: includes/api/api-helpers.php:2973
msgid "File size must not exceed %s."
msgstr "Размер файла не должен превышать %s."

#: includes/api/api-helpers.php:2969
msgid "File size must be at least %s."
msgstr "Размер файла должен быть не менее чем %s."

#: includes/api/api-helpers.php:2956
msgid "Image height must not exceed %dpx."
msgstr "Высота изображения не должна превышать %d px."

#: includes/api/api-helpers.php:2952
msgid "Image height must be at least %dpx."
msgstr "Высота изображения должна быть не менее %d px."

#: includes/api/api-helpers.php:2940
msgid "Image width must not exceed %dpx."
msgstr "Ширина изображения не должна превышать %d px."

#: includes/api/api-helpers.php:2936
msgid "Image width must be at least %dpx."
msgstr "Ширина изображения должна быть не менее %d px."

#: includes/api/api-helpers.php:1425 includes/api/api-term.php:140
msgid "(no title)"
msgstr "(без названия)"

#: includes/api/api-helpers.php:781
msgid "Full Size"
msgstr "Полный"

#: includes/api/api-helpers.php:746
msgid "Large"
msgstr "Большой"

#: includes/api/api-helpers.php:745
msgid "Medium"
msgstr "Средний"

#: includes/api/api-helpers.php:744
msgid "Thumbnail"
msgstr "Миниатюра"

#: includes/acf-field-functions.php:854
#: includes/admin/post-types/admin-field-group.php:95
msgid "(no label)"
msgstr "(без этикетки)"

#: includes/fields/class-acf-field-textarea.php:135
msgid "Sets the textarea height"
msgstr "Задает высоту текстовой области"

#: includes/fields/class-acf-field-textarea.php:134
msgid "Rows"
msgstr "Строки"

#: includes/fields/class-acf-field-textarea.php:22
msgid "Text Area"
msgstr "Область текста"

#: includes/fields/class-acf-field-checkbox.php:421
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Добавьте дополнительный флажок, чтобы переключить все варианты"

#: includes/fields/class-acf-field-checkbox.php:383
msgid "Save 'custom' values to the field's choices"
msgstr "Сохранить \"пользовательские\" значения для выбора поля"

#: includes/fields/class-acf-field-checkbox.php:372
msgid "Allow 'custom' values to be added"
msgstr "Разрешить добавление «пользовательских» значений"

#: includes/fields/class-acf-field-checkbox.php:35
msgid "Add new choice"
msgstr "Добавить новый выбор"

#: includes/fields/class-acf-field-checkbox.php:157
msgid "Toggle All"
msgstr "Переключить все"

#: includes/fields/class-acf-field-page_link.php:487
msgid "Allow Archives URLs"
msgstr "Разрешить URL-адреса архивов"

#: includes/fields/class-acf-field-page_link.php:196
msgid "Archives"
msgstr "Архивы"

#: includes/fields/class-acf-field-page_link.php:22
msgid "Page Link"
msgstr "Ссылка на страницу"

#: includes/fields/class-acf-field-taxonomy.php:884
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Добавить"

#: includes/admin/views/acf-field-group/fields.php:53
#: includes/fields/class-acf-field-taxonomy.php:854
msgid "Name"
msgstr "Имя"

#: includes/fields/class-acf-field-taxonomy.php:839
msgid "%s added"
msgstr "%s добавлен"

#: includes/fields/class-acf-field-taxonomy.php:803
msgid "%s already exists"
msgstr "%s уже существует"

#: includes/fields/class-acf-field-taxonomy.php:791
msgid "User unable to add new %s"
msgstr "У пользователя нет возможности добавить новый %s"

#: includes/fields/class-acf-field-taxonomy.php:678
msgid "Term ID"
msgstr "ID термина"

#: includes/fields/class-acf-field-taxonomy.php:677
msgid "Term Object"
msgstr "Объект термина"

#: includes/fields/class-acf-field-taxonomy.php:662
msgid "Load value from posts terms"
msgstr "Загрузить значения из терминов записей"

#: includes/fields/class-acf-field-taxonomy.php:661
msgid "Load Terms"
msgstr "Загрузить термины"

#: includes/fields/class-acf-field-taxonomy.php:651
msgid "Connect selected terms to the post"
msgstr "Связать выбранные термины с записью"

#: includes/fields/class-acf-field-taxonomy.php:650
msgid "Save Terms"
msgstr "Сохранение терминов"

#: includes/fields/class-acf-field-taxonomy.php:640
msgid "Allow new terms to be created whilst editing"
msgstr "Разрешить создание новых терминов во время редактирования"

#: includes/fields/class-acf-field-taxonomy.php:639
msgid "Create Terms"
msgstr "Создание терминов"

#: includes/fields/class-acf-field-taxonomy.php:698
msgid "Radio Buttons"
msgstr "Кнопки-переключатели"

#: includes/fields/class-acf-field-taxonomy.php:697
msgid "Single Value"
msgstr "Одиночная значение"

#: includes/fields/class-acf-field-taxonomy.php:695
msgid "Multi Select"
msgstr "Множественный выбор"

#: includes/fields/class-acf-field-checkbox.php:22
#: includes/fields/class-acf-field-taxonomy.php:694
msgid "Checkbox"
msgstr "Чекбокс"

#: includes/fields/class-acf-field-taxonomy.php:693
msgid "Multiple Values"
msgstr "Несколько значений"

#: includes/fields/class-acf-field-taxonomy.php:688
msgid "Select the appearance of this field"
msgstr "Выберите способ отображения поля"

#: includes/fields/class-acf-field-taxonomy.php:687
msgid "Appearance"
msgstr "Внешний вид"

#: includes/fields/class-acf-field-taxonomy.php:629
msgid "Select the taxonomy to be displayed"
msgstr "Выберите таксономию для отображения"

#: includes/fields/class-acf-field-taxonomy.php:593
msgctxt "No Terms"
msgid "No %s"
msgstr "Нет %s"

#: includes/fields/class-acf-field-number.php:240
msgid "Value must be equal to or lower than %d"
msgstr "Значение должно быть равным или меньшим чем %d"

#: includes/fields/class-acf-field-number.php:235
msgid "Value must be equal to or higher than %d"
msgstr "Значение должно быть равным или больше чем %d"

#: includes/fields/class-acf-field-number.php:223
msgid "Value must be a number"
msgstr "Значение должно быть числом"

#: includes/fields/class-acf-field-number.php:22
msgid "Number"
msgstr "Число"

#: includes/fields/class-acf-field-radio.php:254
msgid "Save 'other' values to the field's choices"
msgstr "Сохранить значения \"другое\" в выборы поля"

#: includes/fields/class-acf-field-radio.php:243
msgid "Add 'other' choice to allow for custom values"
msgstr "Выберите значение \"Другое\", чтобы разрешить настраиваемые значения"

#: includes/admin/views/global/navigation.php:199
msgid "Other"
msgstr "Другое"

#: includes/fields/class-acf-field-radio.php:22
msgid "Radio Button"
msgstr "Кнопка-переключатель"

#: includes/fields/class-acf-field-accordion.php:106
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Определяет конечную точку предыдущего аккордеона. Данный аккордеон будет "
"невидим."

#: includes/fields/class-acf-field-accordion.php:95
msgid "Allow this accordion to open without closing others."
msgstr "Позвольте этому аккордеону открываться, не закрывая другие."

#: includes/fields/class-acf-field-accordion.php:94
msgid "Multi-Expand"
msgstr "Многократное расширение"

#: includes/fields/class-acf-field-accordion.php:84
msgid "Display this accordion as open on page load."
msgstr "Отображать этот аккордеон как открытый при загрузке страницы."

#: includes/fields/class-acf-field-accordion.php:83
msgid "Open"
msgstr "Открыть"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "Аккордеон"

#: includes/fields/class-acf-field-file.php:253
#: includes/fields/class-acf-field-file.php:265
msgid "Restrict which files can be uploaded"
msgstr "Ограничить файлы, которые могут быть загружены"

#: includes/fields/class-acf-field-file.php:207
msgid "File ID"
msgstr "ID файла"

#: includes/fields/class-acf-field-file.php:206
msgid "File URL"
msgstr "URL файла"

#: includes/fields/class-acf-field-file.php:205
msgid "File Array"
msgstr "Массив файлов"

#: includes/fields/class-acf-field-file.php:176
msgid "Add File"
msgstr "Добавить файл"

#: includes/admin/tools/class-acf-admin-tool-import.php:151
#: includes/fields/class-acf-field-file.php:176
msgid "No file selected"
msgstr "Файл не выбран"

#: includes/fields/class-acf-field-file.php:140
msgid "File name"
msgstr "Имя файла"

#: includes/fields/class-acf-field-file.php:57
msgid "Update File"
msgstr "Обновить файл"

#: includes/fields/class-acf-field-file.php:56
msgid "Edit File"
msgstr "Изменить файл"

#: includes/admin/tools/class-acf-admin-tool-import.php:55
#: includes/fields/class-acf-field-file.php:55
msgid "Select File"
msgstr "Выбрать файл"

#: includes/fields/class-acf-field-file.php:22
msgid "File"
msgstr "Файл"

#: includes/fields/class-acf-field-password.php:22
msgid "Password"
msgstr "Пароль"

#: includes/fields/class-acf-field-select.php:363
msgid "Specify the value returned"
msgstr "Укажите возвращаемое значение"

#: includes/fields/class-acf-field-select.php:431
msgid "Use AJAX to lazy load choices?"
msgstr "Использовать AJAX для отложенной загрузки вариантов?"

#: includes/fields/class-acf-field-checkbox.php:333
#: includes/fields/class-acf-field-select.php:352
msgid "Enter each default value on a new line"
msgstr "Введите каждое значение по умолчанию с новой строки"

#: includes/fields/class-acf-field-select.php:227 includes/media.php:48
msgctxt "verb"
msgid "Select"
msgstr "Выпадающий список"

#: includes/fields/class-acf-field-select.php:101
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Загрузка не удалась"

#: includes/fields/class-acf-field-select.php:100
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Поиск&hellip;"

#: includes/fields/class-acf-field-select.php:99
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Загрузить больше результатов&hellip;"

#. translators: %d - maximum number of items that can be selected in the select
#. field
#: includes/fields/class-acf-field-select.php:98
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Вы можете выбрать только %d элементов"

#: includes/fields/class-acf-field-select.php:96
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Можно выбрать только 1 элемент"

#. translators: %d - number of characters that should be removed from select
#. field
#: includes/fields/class-acf-field-select.php:95
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Удалите %d символов"

#: includes/fields/class-acf-field-select.php:93
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Удалите 1 символ"

#. translators: %d - number of characters to enter into select field input
#: includes/fields/class-acf-field-select.php:92
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Введите %d или больше символов"

#: includes/fields/class-acf-field-select.php:90
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Введите 1 или более символов"

#: includes/fields/class-acf-field-select.php:89
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Соответствий не найдено"

#. translators: %d - number of results available in select field
#: includes/fields/class-acf-field-select.php:88
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d значений доступно, используйте клавиши вверх и вниз для навигации."

#: includes/fields/class-acf-field-select.php:86
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Доступен один результат, нажмите Enter, чтобы выбрать его."

#: includes/fields/class-acf-field-select.php:22
#: includes/fields/class-acf-field-taxonomy.php:699
msgctxt "noun"
msgid "Select"
msgstr "Выбрать"

#: includes/fields/class-acf-field-user.php:102
msgid "User ID"
msgstr "ID пользователя"

#: includes/fields/class-acf-field-user.php:101
msgid "User Object"
msgstr "Объект пользователя"

#: includes/fields/class-acf-field-user.php:100
msgid "User Array"
msgstr "Массив пользователя"

#: includes/fields/class-acf-field-user.php:88
msgid "All user roles"
msgstr "Все роли пользователей"

#: includes/fields/class-acf-field-user.php:80
msgid "Filter by Role"
msgstr "Фильтровать по роли"

#: includes/fields/class-acf-field-user.php:15 includes/locations.php:103
msgid "User"
msgstr "Пользователь"

#: includes/fields/class-acf-field-separator.php:22
msgid "Separator"
msgstr "Разделитель"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Select Color"
msgstr "Выбрать цвет"

#: includes/admin/post-types/admin-post-type.php:127
#: includes/admin/post-types/admin-taxonomy.php:129
#: includes/fields/class-acf-field-color_picker.php:67
msgid "Default"
msgstr "По умолчанию"

#: includes/admin/views/acf-post-type/advanced-settings.php:89
#: includes/admin/views/acf-taxonomy/advanced-settings.php:141
#: includes/fields/class-acf-field-color_picker.php:65
msgid "Clear"
msgstr "Сброс"

#: includes/fields/class-acf-field-color_picker.php:22
msgid "Color Picker"
msgstr "Цветовая палитра"

#: includes/fields/class-acf-field-date_time_picker.php:82
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "ПП"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "ДП"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Выбрать"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Готово"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Сейчас"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Часовой пояс"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Микросекунда"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Миллисекунда"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Секунда"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Минута"

#: includes/fields/class-acf-field-date_time_picker.php:67
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Час"

#: includes/fields/class-acf-field-date_time_picker.php:66
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Время"

#: includes/fields/class-acf-field-date_time_picker.php:65
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Выберите время"

#: includes/fields/class-acf-field-date_time_picker.php:22
msgid "Date Time Picker"
msgstr "Выбор даты и времени"

#: includes/fields/class-acf-field-accordion.php:105
msgid "Endpoint"
msgstr "Конечная точка"

#: includes/admin/views/acf-field-group/options.php:130
#: includes/fields/class-acf-field-tab.php:112
msgid "Left aligned"
msgstr "Выровнено по левому краю"

#: includes/admin/views/acf-field-group/options.php:129
#: includes/fields/class-acf-field-tab.php:111
msgid "Top aligned"
msgstr "Выровнено по верхнему краю"

#: includes/fields/class-acf-field-tab.php:107
msgid "Placement"
msgstr "Расположение"

#: includes/fields/class-acf-field-tab.php:23
msgid "Tab"
msgstr "Вкладка"

#: includes/fields/class-acf-field-url.php:138
msgid "Value must be a valid URL"
msgstr "Значение должно быть допустимым URL"

#: includes/fields/class-acf-field-link.php:153
msgid "Link URL"
msgstr "URL ссылки"

#: includes/fields/class-acf-field-link.php:152
msgid "Link Array"
msgstr "Массив ссылок"

#: includes/fields/class-acf-field-link.php:124
msgid "Opens in a new window/tab"
msgstr "Откроется на новой вкладке"

#: includes/fields/class-acf-field-link.php:119
msgid "Select Link"
msgstr "Выбрать ссылку"

#: includes/fields/class-acf-field-link.php:22
msgid "Link"
msgstr "Ссылка"

#: includes/fields/class-acf-field-email.php:22
msgid "Email"
msgstr "Email"

#: includes/fields/class-acf-field-number.php:173
#: includes/fields/class-acf-field-range.php:206
msgid "Step Size"
msgstr "Шаг изменения"

#: includes/fields/class-acf-field-number.php:143
#: includes/fields/class-acf-field-range.php:184
msgid "Maximum Value"
msgstr "Макс. значение"

#: includes/fields/class-acf-field-number.php:133
#: includes/fields/class-acf-field-range.php:173
msgid "Minimum Value"
msgstr "Минимальное значение"

#: includes/fields/class-acf-field-range.php:22
msgid "Range"
msgstr "Диапазон"

#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:350
#: includes/fields/class-acf-field-radio.php:210
#: includes/fields/class-acf-field-select.php:370
msgid "Both (Array)"
msgstr "Оба (массив)"

#: includes/admin/views/acf-field-group/fields.php:52
#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:349
#: includes/fields/class-acf-field-radio.php:209
#: includes/fields/class-acf-field-select.php:369
msgid "Label"
msgstr "Этикетка"

#: includes/fields/class-acf-field-button-group.php:163
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:208
#: includes/fields/class-acf-field-select.php:368
msgid "Value"
msgstr "Значение"

#: includes/fields/class-acf-field-button-group.php:211
#: includes/fields/class-acf-field-checkbox.php:411
#: includes/fields/class-acf-field-radio.php:282
msgid "Vertical"
msgstr "Вертикально"

#: includes/fields/class-acf-field-button-group.php:210
#: includes/fields/class-acf-field-checkbox.php:412
#: includes/fields/class-acf-field-radio.php:283
msgid "Horizontal"
msgstr "Горизонтально"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "red : Red"
msgstr "red : Красный"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Для большего контроля вы можете указать и значение, и этикетку следующим "
"образом:"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "Enter each choice on a new line."
msgstr "Введите каждый вариант с новой строки."

#: includes/fields/class-acf-field-button-group.php:137
#: includes/fields/class-acf-field-checkbox.php:322
#: includes/fields/class-acf-field-radio.php:182
#: includes/fields/class-acf-field-select.php:340
msgid "Choices"
msgstr "Варианты"

#: includes/fields/class-acf-field-button-group.php:23
msgid "Button Group"
msgstr "Группа кнопок"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-page_link.php:519
#: includes/fields/class-acf-field-post_object.php:432
#: includes/fields/class-acf-field-radio.php:228
#: includes/fields/class-acf-field-select.php:399
#: includes/fields/class-acf-field-taxonomy.php:708
#: includes/fields/class-acf-field-user.php:132
msgid "Allow Null"
msgstr "Разрешить Null"

#: includes/fields/class-acf-field-page_link.php:273
#: includes/fields/class-acf-field-post_object.php:254
#: includes/fields/class-acf-field-taxonomy.php:872
msgid "Parent"
msgstr "Родитель"

#: includes/fields/class-acf-field-wysiwyg.php:367
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "TinyMCE не будет инициализирован, пока не будет нажато поле"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Delay Initialization"
msgstr "Задержка инициализации"

#: includes/fields/class-acf-field-wysiwyg.php:355
msgid "Show Media Upload Buttons"
msgstr "Показать кнопки загрузки медиа файлов"

#: includes/fields/class-acf-field-wysiwyg.php:339
msgid "Toolbar"
msgstr "Верхняя панель"

#: includes/fields/class-acf-field-wysiwyg.php:331
msgid "Text Only"
msgstr "Только текст"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual Only"
msgstr "Только визуально"

#: includes/fields/class-acf-field-wysiwyg.php:329
msgid "Visual & Text"
msgstr "Визуально и текст"

#: includes/fields/class-acf-field-icon_picker.php:237
#: includes/fields/class-acf-field-wysiwyg.php:324
msgid "Tabs"
msgstr "Вкладки"

#: includes/fields/class-acf-field-wysiwyg.php:268
msgid "Click to initialize TinyMCE"
msgstr "Нажмите для инициализации TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:262
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Текст"

#: includes/fields/class-acf-field-wysiwyg.php:261
msgid "Visual"
msgstr "Визуально"

#: includes/fields/class-acf-field-text.php:181
#: includes/fields/class-acf-field-textarea.php:217
msgid "Value must not exceed %d characters"
msgstr "Значение не должно превышать %d символов"

#: includes/fields/class-acf-field-text.php:116
#: includes/fields/class-acf-field-textarea.php:114
msgid "Leave blank for no limit"
msgstr "Оставьте пустым, чтобы не ограничивать"

#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:113
msgid "Character Limit"
msgstr "Ограничение кол-ва символов"

#: includes/fields/class-acf-field-email.php:144
#: includes/fields/class-acf-field-number.php:194
#: includes/fields/class-acf-field-password.php:95
#: includes/fields/class-acf-field-range.php:228
#: includes/fields/class-acf-field-text.php:156
msgid "Appears after the input"
msgstr "Появляется после ввода"

#: includes/fields/class-acf-field-email.php:143
#: includes/fields/class-acf-field-number.php:193
#: includes/fields/class-acf-field-password.php:94
#: includes/fields/class-acf-field-range.php:227
#: includes/fields/class-acf-field-text.php:155
msgid "Append"
msgstr "Добавить"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:184
#: includes/fields/class-acf-field-password.php:85
#: includes/fields/class-acf-field-range.php:218
#: includes/fields/class-acf-field-text.php:146
msgid "Appears before the input"
msgstr "Появляется перед вводом"

#: includes/fields/class-acf-field-email.php:133
#: includes/fields/class-acf-field-number.php:183
#: includes/fields/class-acf-field-password.php:84
#: includes/fields/class-acf-field-range.php:217
#: includes/fields/class-acf-field-text.php:145
msgid "Prepend"
msgstr "Добавить в начало"

#: includes/fields/class-acf-field-email.php:124
#: includes/fields/class-acf-field-number.php:164
#: includes/fields/class-acf-field-password.php:75
#: includes/fields/class-acf-field-text.php:136
#: includes/fields/class-acf-field-textarea.php:146
#: includes/fields/class-acf-field-url.php:105
msgid "Appears within the input"
msgstr "Появляется перед полем ввода"

#: includes/fields/class-acf-field-email.php:123
#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-password.php:74
#: includes/fields/class-acf-field-text.php:135
#: includes/fields/class-acf-field-textarea.php:145
#: includes/fields/class-acf-field-url.php:104
msgid "Placeholder Text"
msgstr "Текст-заполнитель"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-email.php:104
#: includes/fields/class-acf-field-number.php:114
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-range.php:154
#: includes/fields/class-acf-field-text.php:96
#: includes/fields/class-acf-field-textarea.php:94
#: includes/fields/class-acf-field-url.php:85
#: includes/fields/class-acf-field-wysiwyg.php:292
msgid "Appears when creating a new post"
msgstr "Появляется при создании новой записи"

#: includes/fields/class-acf-field-text.php:22
msgid "Text"
msgstr "Текст"

#: includes/fields/class-acf-field-relationship.php:753
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s требует выбора как минимум %2$s элемента"
msgstr[1] "%1$s требует выбора как минимум %2$s элемента"
msgstr[2] "%1$s требует выбора как минимум %2$s элементов"

#: includes/fields/class-acf-field-post_object.php:402
#: includes/fields/class-acf-field-relationship.php:616
msgid "Post ID"
msgstr "ID записи"

#: includes/fields/class-acf-field-post_object.php:15
#: includes/fields/class-acf-field-post_object.php:401
#: includes/fields/class-acf-field-relationship.php:615
msgid "Post Object"
msgstr "Объект записи"

#: includes/fields/class-acf-field-relationship.php:648
msgid "Maximum Posts"
msgstr "Макс. кол-во записей"

#: includes/fields/class-acf-field-relationship.php:638
msgid "Minimum Posts"
msgstr "Мин. кол-во записей"

#: includes/admin/views/acf-field-group/options.php:183
#: includes/admin/views/acf-post-type/advanced-settings.php:29
#: includes/fields/class-acf-field-relationship.php:673
msgid "Featured Image"
msgstr "Изображение записи"

#: includes/fields/class-acf-field-relationship.php:669
msgid "Selected elements will be displayed in each result"
msgstr "Выбранные элементы будут отображены в каждом результате"

#: includes/fields/class-acf-field-relationship.php:668
msgid "Elements"
msgstr "Элементы"

#: includes/fields/class-acf-field-relationship.php:602
#: includes/fields/class-acf-field-taxonomy.php:20
#: includes/fields/class-acf-field-taxonomy.php:628
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Таксономия"

#: includes/fields/class-acf-field-relationship.php:601
#: includes/locations/class-acf-location-post-type.php:22
#: includes/post-types/class-acf-post-type.php:92
msgid "Post Type"
msgstr "Тип записи"

#: includes/fields/class-acf-field-relationship.php:595
msgid "Filters"
msgstr "Фильтры"

#: includes/fields/class-acf-field-page_link.php:480
#: includes/fields/class-acf-field-post_object.php:389
#: includes/fields/class-acf-field-relationship.php:588
msgid "All taxonomies"
msgstr "Все таксономии"

#: includes/fields/class-acf-field-page_link.php:472
#: includes/fields/class-acf-field-post_object.php:381
#: includes/fields/class-acf-field-relationship.php:580
msgid "Filter by Taxonomy"
msgstr "Фильтрация по таксономии"

#: includes/fields/class-acf-field-page_link.php:450
#: includes/fields/class-acf-field-post_object.php:359
#: includes/fields/class-acf-field-relationship.php:558
msgid "All post types"
msgstr "Все типы записи"

#: includes/fields/class-acf-field-page_link.php:442
#: includes/fields/class-acf-field-post_object.php:351
#: includes/fields/class-acf-field-relationship.php:550
msgid "Filter by Post Type"
msgstr "Фильтрация по типу записей"

#: includes/fields/class-acf-field-relationship.php:450
msgid "Search..."
msgstr "Поиск..."

#: includes/fields/class-acf-field-relationship.php:380
msgid "Select taxonomy"
msgstr "Выбрать таксономию"

#: includes/fields/class-acf-field-relationship.php:372
msgid "Select post type"
msgstr "Выбрать тип записи"

#: includes/fields/class-acf-field-relationship.php:78
msgid "No matches found"
msgstr "Соответствий не найдено"

#: includes/fields/class-acf-field-relationship.php:77
msgid "Loading"
msgstr "Загрузка"

#: includes/fields/class-acf-field-relationship.php:76
msgid "Maximum values reached ( {max} values )"
msgstr "Достигнуты макс. значения ( {max} values )"

#: includes/fields/class-acf-field-relationship.php:17
msgid "Relationship"
msgstr "Родственные связи"

#: includes/fields/class-acf-field-file.php:277
#: includes/fields/class-acf-field-image.php:307
msgid "Comma separated list. Leave blank for all types"
msgstr ""
"Для разделения типов файлов используйте запятые. Оставьте поле пустым для "
"разрешения загрузки всех файлов"

#: includes/fields/class-acf-field-file.php:276
#: includes/fields/class-acf-field-image.php:306
msgid "Allowed File Types"
msgstr "Разрешенные типы файлов"

#: includes/fields/class-acf-field-file.php:264
#: includes/fields/class-acf-field-image.php:270
msgid "Maximum"
msgstr "Максимум"

#: includes/fields/class-acf-field-file.php:144
#: includes/fields/class-acf-field-file.php:256
#: includes/fields/class-acf-field-file.php:268
#: includes/fields/class-acf-field-image.php:261
#: includes/fields/class-acf-field-image.php:297
msgid "File size"
msgstr "Размер файла"

#: includes/fields/class-acf-field-image.php:235
#: includes/fields/class-acf-field-image.php:271
msgid "Restrict which images can be uploaded"
msgstr "Ограничить изображения, которые могут быть загружены"

#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-image.php:234
msgid "Minimum"
msgstr "Минимум"

#: includes/fields/class-acf-field-file.php:222
#: includes/fields/class-acf-field-image.php:200
msgid "Uploaded to post"
msgstr "Загружено в запись"

#: includes/fields/class-acf-field-file.php:221
#: includes/fields/class-acf-field-image.php:199
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "Все"

#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:194
msgid "Limit the media library choice"
msgstr "Ограничить выбор из библиотеки файлов"

#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:193
msgid "Library"
msgstr "Библиотека"

#: includes/fields/class-acf-field-image.php:326
msgid "Preview Size"
msgstr "Размер предпросмотра"

#: includes/fields/class-acf-field-image.php:185
msgid "Image ID"
msgstr "ID изображения"

#: includes/fields/class-acf-field-image.php:184
msgid "Image URL"
msgstr "URL изображения"

#: includes/fields/class-acf-field-image.php:183
msgid "Image Array"
msgstr "Массив изображения"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-checkbox.php:343
#: includes/fields/class-acf-field-file.php:200
#: includes/fields/class-acf-field-link.php:147
#: includes/fields/class-acf-field-radio.php:203
msgid "Specify the returned value on front end"
msgstr "Укажите возвращаемое значение на фронтедне"

#: includes/fields/class-acf-field-button-group.php:157
#: includes/fields/class-acf-field-checkbox.php:342
#: includes/fields/class-acf-field-file.php:199
#: includes/fields/class-acf-field-link.php:146
#: includes/fields/class-acf-field-radio.php:202
#: includes/fields/class-acf-field-taxonomy.php:672
msgid "Return Value"
msgstr "Возвращаемое значение"

#: includes/fields/class-acf-field-image.php:155
msgid "Add Image"
msgstr "Добавить изображение"

#: includes/fields/class-acf-field-image.php:155
msgid "No image selected"
msgstr "Изображение не выбрано"

#: includes/assets.php:353 includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:135
#: includes/fields/class-acf-field-link.php:124
msgid "Remove"
msgstr "Удалить"

#: includes/admin/views/acf-field-group/field.php:89
#: includes/fields/class-acf-field-file.php:150
#: includes/fields/class-acf-field-image.php:133
#: includes/fields/class-acf-field-link.php:124
msgid "Edit"
msgstr "Изменить"

#: includes/fields/class-acf-field-image.php:63 includes/media.php:55
msgid "All images"
msgstr "Все изображения"

#: includes/fields/class-acf-field-image.php:62
msgid "Update Image"
msgstr "Обновить изображение"

#: includes/fields/class-acf-field-image.php:61
msgid "Edit Image"
msgstr "Редактировать"

#: includes/fields/class-acf-field-image.php:60
msgid "Select Image"
msgstr "Выбрать изображение"

#: includes/fields/class-acf-field-image.php:22
msgid "Image"
msgstr "Изображение"

#: includes/fields/class-acf-field-message.php:113
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""
"Разрешить HTML-разметке отображаться в виде видимого текста вместо отрисовки"

#: includes/fields/class-acf-field-message.php:112
msgid "Escape HTML"
msgstr "Escape HTML"

#: includes/fields/class-acf-field-message.php:104
#: includes/fields/class-acf-field-textarea.php:162
msgid "No Formatting"
msgstr "Без форматирования"

#: includes/fields/class-acf-field-message.php:103
#: includes/fields/class-acf-field-textarea.php:161
msgid "Automatically add &lt;br&gt;"
msgstr "Автоматически добавлять &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:102
#: includes/fields/class-acf-field-textarea.php:160
msgid "Automatically add paragraphs"
msgstr "Автоматически добавлять абзацы"

#: includes/fields/class-acf-field-message.php:98
#: includes/fields/class-acf-field-textarea.php:156
msgid "Controls how new lines are rendered"
msgstr "Управляет отрисовкой новых линий"

#: includes/fields/class-acf-field-message.php:97
#: includes/fields/class-acf-field-textarea.php:155
msgid "New Lines"
msgstr "Новые строки"

#: includes/fields/class-acf-field-date_picker.php:221
#: includes/fields/class-acf-field-date_time_picker.php:208
msgid "Week Starts On"
msgstr "Неделя начинается с"

#: includes/fields/class-acf-field-date_picker.php:190
msgid "The format used when saving a value"
msgstr "Формат, используемый при сохранении значения"

#: includes/fields/class-acf-field-date_picker.php:189
msgid "Save Format"
msgstr "Сохранить формат"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Нед."

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Назад"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Далее"

#: includes/fields/class-acf-field-date_picker.php:58
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Сегодня"

#: includes/fields/class-acf-field-date_picker.php:57
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Готово"

#: includes/fields/class-acf-field-date_picker.php:22
msgid "Date Picker"
msgstr "Выбор даты"

#: includes/fields/class-acf-field-image.php:238
#: includes/fields/class-acf-field-image.php:274
#: includes/fields/class-acf-field-oembed.php:241
msgid "Width"
msgstr "Ширина"

#: includes/fields/class-acf-field-oembed.php:238
#: includes/fields/class-acf-field-oembed.php:250
msgid "Embed Size"
msgstr "Размер встраивания"

#: includes/fields/class-acf-field-oembed.php:198
msgid "Enter URL"
msgstr "Введите URL"

#: includes/fields/class-acf-field-oembed.php:22
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-true_false.php:172
msgid "Text shown when inactive"
msgstr "Текст отображается, когда он неактивен"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Off Text"
msgstr "Отключить текст"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "Текст, отображаемый при активности"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "На тексте"

#: includes/fields/class-acf-field-select.php:420
#: includes/fields/class-acf-field-true_false.php:187
msgid "Stylized UI"
msgstr "Стилизованный интерфейс"

#: includes/fields/class-acf-field-button-group.php:147
#: includes/fields/class-acf-field-checkbox.php:332
#: includes/fields/class-acf-field-color_picker.php:144
#: includes/fields/class-acf-field-email.php:103
#: includes/fields/class-acf-field-number.php:113
#: includes/fields/class-acf-field-radio.php:192
#: includes/fields/class-acf-field-range.php:153
#: includes/fields/class-acf-field-select.php:351
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:93
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:84
#: includes/fields/class-acf-field-wysiwyg.php:291
msgid "Default Value"
msgstr "Значение по умолчанию"

#: includes/fields/class-acf-field-true_false.php:126
msgid "Displays text alongside the checkbox"
msgstr "Отображать текст рядом с флажком"

#: includes/fields/class-acf-field-message.php:23
#: includes/fields/class-acf-field-message.php:87
#: includes/fields/class-acf-field-true_false.php:125
msgid "Message"
msgstr "Сообщение"

#: includes/assets.php:352 includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:175
#: src/Site_Health/Site_Health.php:281 src/Site_Health/Site_Health.php:338
msgid "No"
msgstr "Нет"

#: includes/assets.php:351 includes/fields/class-acf-field-true_false.php:76
#: includes/fields/class-acf-field-true_false.php:159
#: src/Site_Health/Site_Health.php:280 src/Site_Health/Site_Health.php:338
msgid "Yes"
msgstr "Да"

#: includes/fields/class-acf-field-true_false.php:22
msgid "True / False"
msgstr "True / False"

#: includes/fields/class-acf-field-group.php:415
msgid "Row"
msgstr "Строка"

#: includes/fields/class-acf-field-group.php:414
msgid "Table"
msgstr "Таблица"

#: includes/admin/post-types/admin-field-group.php:158
#: includes/fields/class-acf-field-group.php:413
msgid "Block"
msgstr "Блок"

#: includes/fields/class-acf-field-group.php:408
msgid "Specify the style used to render the selected fields"
msgstr "Укажите стиль, используемый для отрисовки выбранных полей"

#: includes/fields.php:330 includes/fields/class-acf-field-button-group.php:204
#: includes/fields/class-acf-field-checkbox.php:405
#: includes/fields/class-acf-field-group.php:407
#: includes/fields/class-acf-field-radio.php:276
msgid "Layout"
msgstr "Макет"

#: includes/fields/class-acf-field-group.php:391
msgid "Sub Fields"
msgstr "Вложенные поля"

#: includes/fields/class-acf-field-group.php:22
msgid "Group"
msgstr "Группа"

#: includes/fields/class-acf-field-google-map.php:222
msgid "Customize the map height"
msgstr "Настройка высоты карты"

#: includes/fields/class-acf-field-google-map.php:221
#: includes/fields/class-acf-field-image.php:249
#: includes/fields/class-acf-field-image.php:285
#: includes/fields/class-acf-field-oembed.php:253
msgid "Height"
msgstr "Высота"

#: includes/fields/class-acf-field-google-map.php:210
msgid "Set the initial zoom level"
msgstr "Укажите начальный масштаб"

#: includes/fields/class-acf-field-google-map.php:209
msgid "Zoom"
msgstr "Увеличить"

#: includes/fields/class-acf-field-google-map.php:183
#: includes/fields/class-acf-field-google-map.php:196
msgid "Center the initial map"
msgstr "Центрировать начальную карту"

#: includes/fields/class-acf-field-google-map.php:182
#: includes/fields/class-acf-field-google-map.php:195
msgid "Center"
msgstr "По центру"

#: includes/fields/class-acf-field-google-map.php:154
msgid "Search for address..."
msgstr "Поиск по адресу..."

#: includes/fields/class-acf-field-google-map.php:151
msgid "Find current location"
msgstr "Определить текущее местоположение"

#: includes/fields/class-acf-field-google-map.php:150
msgid "Clear location"
msgstr "Очистить местоположение"

#: includes/fields/class-acf-field-google-map.php:149
#: includes/fields/class-acf-field-relationship.php:600
msgid "Search"
msgstr "Поиск"

#: includes/fields/class-acf-field-google-map.php:57
msgid "Sorry, this browser does not support geolocation"
msgstr "Ваш браузер не поддерживает определение местоположения"

#: includes/fields/class-acf-field-google-map.php:22
msgid "Google Map"
msgstr "Google Карта"

#: includes/fields/class-acf-field-date_picker.php:201
#: includes/fields/class-acf-field-date_time_picker.php:189
#: includes/fields/class-acf-field-time_picker.php:122
msgid "The format returned via template functions"
msgstr "Формат, возвращаемый через функции шаблона"

#: includes/fields/class-acf-field-color_picker.php:168
#: includes/fields/class-acf-field-date_picker.php:200
#: includes/fields/class-acf-field-date_time_picker.php:188
#: includes/fields/class-acf-field-icon_picker.php:260
#: includes/fields/class-acf-field-image.php:177
#: includes/fields/class-acf-field-post_object.php:396
#: includes/fields/class-acf-field-relationship.php:610
#: includes/fields/class-acf-field-select.php:362
#: includes/fields/class-acf-field-time_picker.php:121
#: includes/fields/class-acf-field-user.php:95
msgid "Return Format"
msgstr "Формат возврата"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_picker.php:210
#: includes/fields/class-acf-field-date_time_picker.php:180
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:113
#: includes/fields/class-acf-field-time_picker.php:129
msgid "Custom:"
msgstr "Пользовательский:"

#: includes/fields/class-acf-field-date_picker.php:171
#: includes/fields/class-acf-field-date_time_picker.php:171
#: includes/fields/class-acf-field-time_picker.php:106
msgid "The format displayed when editing a post"
msgstr "Формат, отображаемый при редактировании записи"

#: includes/fields/class-acf-field-date_picker.php:170
#: includes/fields/class-acf-field-date_time_picker.php:170
#: includes/fields/class-acf-field-time_picker.php:105
msgid "Display Format"
msgstr "Отображаемый формат"

#: includes/fields/class-acf-field-time_picker.php:22
msgid "Time Picker"
msgstr "Подборщик времени"

#. translators: counts for inactive field groups
#: acf.php:525
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Неактивен <span class=\"count\">(%s)</span>"
msgstr[1] "Неактивны <span class=\"count\">(%s)</span>"
msgstr[2] "Неактивно <span class=\"count\">(%s)</span>"

#: acf.php:486
msgid "No Fields found in Trash"
msgstr "Поля не найдены в корзине"

#: acf.php:485
msgid "No Fields found"
msgstr "Поля не найдены"

#: acf.php:484
msgid "Search Fields"
msgstr "Поиск полей"

#: acf.php:483
msgid "View Field"
msgstr "Просмотреть поле"

#: acf.php:482 includes/admin/views/acf-field-group/fields.php:113
msgid "New Field"
msgstr "Новое поле"

#: acf.php:481
msgid "Edit Field"
msgstr "Изменить поле"

#: acf.php:480
msgid "Add New Field"
msgstr "Добавить новое поле"

#: acf.php:478
msgid "Field"
msgstr "Поле"

#: acf.php:477 includes/admin/post-types/admin-field-group.php:179
#: includes/admin/post-types/admin-field-groups.php:93
#: includes/admin/views/acf-field-group/fields.php:32
msgid "Fields"
msgstr "Поля"

#: acf.php:452
msgid "No Field Groups found in Trash"
msgstr "Группы полей не найдены в корзине"

#: acf.php:451
msgid "No Field Groups found"
msgstr "Группы полей не найдены"

#: acf.php:450
msgid "Search Field Groups"
msgstr "Найти группу полей"

#: acf.php:449
msgid "View Field Group"
msgstr "Просмотреть группу полей"

#: acf.php:448
msgid "New Field Group"
msgstr "Новая группа полей"

#: acf.php:447
msgid "Edit Field Group"
msgstr "Редактирование группы полей"

#: acf.php:446
msgid "Add New Field Group"
msgstr "Добавить новую группу полей"

#: acf.php:445 acf.php:479
#: includes/admin/views/acf-post-type/advanced-settings.php:224
#: includes/post-types/class-acf-post-type.php:93
#: includes/post-types/class-acf-taxonomy.php:92
msgid "Add New"
msgstr "Добавить новое"

#: acf.php:444
msgid "Field Group"
msgstr "Группа полей"

#: acf.php:443 includes/admin/post-types/admin-field-groups.php:55
#: includes/admin/post-types/admin-post-types.php:113
#: includes/admin/post-types/admin-taxonomies.php:112
msgid "Field Groups"
msgstr "Группы полей"

#. Description of the plugin
#: acf.php
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr ""
"Настройте WordPress с помощью мощных, профессиональных и интуитивно понятных "
"полей."

#. Plugin URI of the plugin
#: acf.php
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php acf.php:289
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/blocks.php:170
#, fuzzy
#| msgid "%s value is required"
msgid "Block type name is required."
msgstr "%s значение требуется"

#. translators: The name of the block type
#: pro/blocks.php:178
msgid "Block type \"%s\" is already registered."
msgstr ""

#: pro/blocks.php:726
msgid "Switch to Edit"
msgstr ""

#: pro/blocks.php:727
msgid "Switch to Preview"
msgstr ""

#: pro/blocks.php:728
msgid "Change content alignment"
msgstr ""

#. translators: %s: Block type title
#: pro/blocks.php:731
#, fuzzy
#| msgid "Settings"
msgid "%s settings"
msgstr "Настройки"

#: pro/blocks.php:936
msgid "This block contains no editable fields."
msgstr ""

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:942
msgid ""
"Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to "
"this block."
msgstr ""

#: pro/options-page.php:78
msgid "Options Updated"
msgstr "Настройки были обновлены"

#: pro/updates.php:99
#, fuzzy
#| msgid ""
#| "To enable updates, please enter your license key on the <a "
#| "href=\"%s\">Updates</a> page. If you don't have a licence key, please see "
#| "<a href=\"%s\">details & pricing</a>."
msgid ""
"To enable updates, please enter your license key on the <a "
"href=\"%1$s\">Updates</a> page. If you don't have a licence key, please see "
"<a href=\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""
"Для разблокировки обновлений введите ваш лицензионный ключ на странице <a "
"href=\"%s\">Обновление</a>. Если у вас его нет, то ознакомьтесь с <a "
"href=\"%s\" target=\"_blank\">деталями</a>."

#: pro/updates.php:159
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when deactivating your old licence"
msgstr ""

#: pro/updates.php:154
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when connecting to activation server"
msgstr ""

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr ""

#: pro/updates.php:187
#, fuzzy
#| msgid "<b>Error</b>. Could not connect to update server"
msgid ""
"<b>ACF Activation Error</b>. An error occurred when connecting to activation "
"server"
msgstr "<b>Ошибка</b>. Не удалось подключиться к серверу обновлений"

#: pro/updates.php:279
msgid "Check Again"
msgstr "Проверить еще раз"

#: pro/updates.php:593
#, fuzzy
#| msgid "<b>Error</b>. Could not connect to update server"
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr "<b>Ошибка</b>. Не удалось подключиться к серверу обновлений"

#: pro/admin/admin-options-page.php:195
msgid "Publish"
msgstr "Опубликовано"

#: pro/admin/admin-options-page.php:199
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"С этой страницей настроек не связаны группы полей. <a href=\"%s\">Создать "
"группу полей</a>"

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Ошибка</b>. Не удалось подключиться к серверу обновлений"

#: pro/admin/admin-updates.php:212
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""

#: pro/admin/admin-updates.php:199
#, fuzzy
#| msgid ""
#| "Error validating license URL (website does not match). Please re-activate "
#| "your license"
msgid ""
"<b>Error</b>. Your license for this site has expired or been deactivated. "
"Please reactivate your ACF PRO license."
msgstr ""
"Во время проверки лицензии, которая связана с адресом сайта, возникла "
"ошибка. Пожалуйста, выполните активацию снова"

#: pro/fields/class-acf-field-clone.php:27,
#: pro/fields/class-acf-field-repeater.php:31
msgid ""
"Allows you to select and display existing fields. It does not duplicate any "
"fields in the database, but loads and displays the selected fields at run-"
"time. The Clone field can either replace itself with the selected fields or "
"display the selected fields as a group of subfields."
msgstr ""

#: pro/fields/class-acf-field-clone.php:819
msgid "Select one or more fields you wish to clone"
msgstr "Выберите одно или несколько полей, которые вы хотите клонировать"

#: pro/fields/class-acf-field-clone.php:838
msgid "Display"
msgstr "Способ отображения"

#: pro/fields/class-acf-field-clone.php:839
msgid "Specify the style used to render the clone field"
msgstr "Выберите стиль отображения клонированных полей"

#: pro/fields/class-acf-field-clone.php:844
msgid "Group (displays selected fields in a group within this field)"
msgstr ""
"Группа (сгруппировать выбранные поля в одно и выводить вместо текущего)"

#: pro/fields/class-acf-field-clone.php:845
msgid "Seamless (replaces this field with selected fields)"
msgstr "Отдельно (выбранные поля выводятся отдельно вместо текущего)"

#: pro/fields/class-acf-field-clone.php:868
msgid "Labels will be displayed as %s"
msgstr "Ярлыки будут отображаться как %s"

#: pro/fields/class-acf-field-clone.php:873
msgid "Prefix Field Labels"
msgstr "Префикс для ярлыков полей"

#: pro/fields/class-acf-field-clone.php:883
msgid "Values will be saved as %s"
msgstr "Значения будут сохранены как %s"

#: pro/fields/class-acf-field-clone.php:888
msgid "Prefix Field Names"
msgstr "Префикс для названий полей"

#: pro/fields/class-acf-field-clone.php:1005
msgid "Unknown field"
msgstr "Неизвестное поле"

#: pro/fields/class-acf-field-clone.php:1042
msgid "Unknown field group"
msgstr "Неизвестная группа полей"

#: pro/fields/class-acf-field-clone.php:1046
msgid "All fields from %s field group"
msgstr "Все поля группы %s"

#: pro/fields/class-acf-field-flexible-content.php:27
msgid ""
"Allows you to define, create and manage content with total control by "
"creating layouts that contain subfields that content editors can choose from."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:36,
#: pro/fields/class-acf-field-repeater.php:103,
#: pro/fields/class-acf-field-repeater.php:297
msgid "Add Row"
msgstr "Добавить"

#: pro/fields/class-acf-field-flexible-content.php:76,
#: pro/fields/class-acf-field-flexible-content.php:943,
#: pro/fields/class-acf-field-flexible-content.php:1022
msgid "layout"
msgid_plural "layouts"
msgstr[0] "макет"
msgstr[1] "макета"
msgstr[2] "макетов"

#: pro/fields/class-acf-field-flexible-content.php:77
msgid "layouts"
msgstr "макеты"

#: pro/fields/class-acf-field-flexible-content.php:81,
#: pro/fields/class-acf-field-flexible-content.php:942,
#: pro/fields/class-acf-field-flexible-content.php:1021
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Это поле требует как минимум {min} {label}  {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "Это поле ограничено {max} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} доступно (максимум {max})"

#: pro/fields/class-acf-field-flexible-content.php:86
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} требуется (минимум {min})"

#: pro/fields/class-acf-field-flexible-content.php:89
msgid "Flexible Content requires at least 1 layout"
msgstr "Для гибкого содержания требуется как минимум один макет"

#: pro/fields/class-acf-field-flexible-content.php:282
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Нажмите на кнопку \"%s\" ниже для начала создания собственного макета"

#: pro/fields/class-acf-field-flexible-content.php:423
msgid "Add layout"
msgstr "Добавить макет"

#: pro/fields/class-acf-field-flexible-content.php:424
#, fuzzy
#| msgid "Duplicate Layout"
msgid "Duplicate layout"
msgstr "Дублировать макет"

#: pro/fields/class-acf-field-flexible-content.php:425
msgid "Remove layout"
msgstr "Удалить макет"

#: pro/fields/class-acf-field-flexible-content.php:426,
#: pro/fields/class-acf-repeater-table.php:382
msgid "Click to toggle"
msgstr "Нажмите для переключения"

#: pro/fields/class-acf-field-flexible-content.php:562
msgid "Delete Layout"
msgstr "Удалить макет"

#: pro/fields/class-acf-field-flexible-content.php:563
msgid "Duplicate Layout"
msgstr "Дублировать макет"

#: pro/fields/class-acf-field-flexible-content.php:564
msgid "Add New Layout"
msgstr "Добавить новый макет"

#: pro/fields/class-acf-field-flexible-content.php:564
#, fuzzy
#| msgid "Add layout"
msgid "Add Layout"
msgstr "Добавить макет"

#: pro/fields/class-acf-field-flexible-content.php:647
msgid "Min"
msgstr "Минимум"

#: pro/fields/class-acf-field-flexible-content.php:662
msgid "Max"
msgstr "Максимум"

#: pro/fields/class-acf-field-flexible-content.php:705
msgid "Minimum Layouts"
msgstr "Мин. количество блоков"

#: pro/fields/class-acf-field-flexible-content.php:716
msgid "Maximum Layouts"
msgstr "Макс. количество блоков"

#: pro/fields/class-acf-field-flexible-content.php:727,
#: pro/fields/class-acf-field-repeater.php:293
msgid "Button Label"
msgstr "Текст кнопки добавления"

#: pro/fields/class-acf-field-flexible-content.php:1710,
#: pro/fields/class-acf-field-repeater.php:918
msgid "%s must be of type array or null."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:1721
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: pro/fields/class-acf-field-flexible-content.php:1737
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: pro/fields/class-acf-field-gallery.php:27
msgid ""
"An interactive interface for managing a collection of attachments, such as "
"images."
msgstr ""

#: pro/fields/class-acf-field-gallery.php:77
msgid "Add Image to Gallery"
msgstr "Добавление изображений в галерею"

#: pro/fields/class-acf-field-gallery.php:78
msgid "Maximum selection reached"
msgstr "Выбрано максимальное количество изображений"

#: pro/fields/class-acf-field-gallery.php:324
msgid "Length"
msgstr "Длина"

#: pro/fields/class-acf-field-gallery.php:368
msgid "Caption"
msgstr "Подпись"

#: pro/fields/class-acf-field-gallery.php:380
msgid "Alt Text"
msgstr "Текст в ALT"

#: pro/fields/class-acf-field-gallery.php:504
msgid "Add to gallery"
msgstr "Добавить изображения"

#: pro/fields/class-acf-field-gallery.php:508
msgid "Bulk actions"
msgstr "Сортировка"

#: pro/fields/class-acf-field-gallery.php:509
msgid "Sort by date uploaded"
msgstr "По дате загрузки"

#: pro/fields/class-acf-field-gallery.php:510
msgid "Sort by date modified"
msgstr "По дате изменения"

#: pro/fields/class-acf-field-gallery.php:511
msgid "Sort by title"
msgstr "По названию"

#: pro/fields/class-acf-field-gallery.php:512
msgid "Reverse current order"
msgstr "Инвертировать"

#: pro/fields/class-acf-field-gallery.php:524
msgid "Close"
msgstr "Закрыть"

#: pro/fields/class-acf-field-gallery.php:615
msgid "Minimum Selection"
msgstr "Мин. количество изображений"

#: pro/fields/class-acf-field-gallery.php:625
msgid "Maximum Selection"
msgstr "Макс. количество изображений"

#: pro/fields/class-acf-field-gallery.php:707
msgid "Allowed file types"
msgstr "Допустимые типы файлов"

#: pro/fields/class-acf-field-gallery.php:727
msgid "Insert"
msgstr "Добавить"

#: pro/fields/class-acf-field-gallery.php:728
msgid "Specify where new attachments are added"
msgstr "Укажите куда добавлять новые вложения"

#: pro/fields/class-acf-field-gallery.php:732
msgid "Append to the end"
msgstr "Добавлять в конец"

#: pro/fields/class-acf-field-gallery.php:733
msgid "Prepend to the beginning"
msgstr "Добавлять в начало"

#: pro/fields/class-acf-field-repeater.php:66,
#: pro/fields/class-acf-field-repeater.php:463
#, fuzzy
#| msgid "Minimum rows reached ({min} rows)"
msgid "Minimum rows not reached ({min} rows)"
msgstr "Достигнуто минимальное количество ({min} элементов)"

#: pro/fields/class-acf-field-repeater.php:67
msgid "Maximum rows reached ({max} rows)"
msgstr "Достигнуто максимальное количество ({max} элементов)"

#: pro/fields/class-acf-field-repeater.php:68
#, fuzzy
#| msgid "Error loading update"
msgid "Error loading page"
msgstr "Возникла ошибка при загрузке обновления"

#: pro/fields/class-acf-field-repeater.php:69
msgid "Order will be assigned upon save"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:196
msgid "Useful for fields with a large number of rows."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:207
#, fuzzy
#| msgid "Posts Page"
msgid "Rows Per Page"
msgstr "Страница записей"

#: pro/fields/class-acf-field-repeater.php:208
#, fuzzy
#| msgid "Select the taxonomy to be displayed"
msgid "Set the number of rows to be displayed on a page."
msgstr "Выберите таксономию для отображения"

#: pro/fields/class-acf-field-repeater.php:240
msgid "Minimum Rows"
msgstr "Мин. количество элементов"

#: pro/fields/class-acf-field-repeater.php:251
msgid "Maximum Rows"
msgstr "Макс. количество элементов"

#: pro/fields/class-acf-field-repeater.php:281
msgid "Collapsed"
msgstr "Сокращенный заголовок"

#: pro/fields/class-acf-field-repeater.php:282
msgid "Select a sub field to show when row is collapsed"
msgstr ""
"Выберите поле, которое будет отображаться в качестве заголовка при "
"сворачивании блока"

#: pro/fields/class-acf-field-repeater.php:1060
msgid "Invalid field key or name."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:1069
msgid "There was an error retrieving the field."
msgstr ""

#: pro/fields/class-acf-repeater-table.php:369
#, fuzzy
#| msgid "Drag to reorder"
msgid "Click to reorder"
msgstr "Потяните для изменения порядка"

#: pro/fields/class-acf-repeater-table.php:402
msgid "Add row"
msgstr "Добавить"

#: pro/fields/class-acf-repeater-table.php:403
#, fuzzy
#| msgid "Duplicate"
msgid "Duplicate row"
msgstr "Дублировать"

#: pro/fields/class-acf-repeater-table.php:404
msgid "Remove row"
msgstr "Удалить"

#: pro/fields/class-acf-repeater-table.php:448,
#: pro/fields/class-acf-repeater-table.php:465,
#: pro/fields/class-acf-repeater-table.php:466
#, fuzzy
#| msgid "Current User"
msgid "Current Page"
msgstr "Текущий пользователь"

#: pro/fields/class-acf-repeater-table.php:456,
#: pro/fields/class-acf-repeater-table.php:457
#, fuzzy
#| msgid "Front Page"
msgid "First Page"
msgstr "Главная страница"

#: pro/fields/class-acf-repeater-table.php:460,
#: pro/fields/class-acf-repeater-table.php:461
#, fuzzy
#| msgid "Posts Page"
msgid "Previous Page"
msgstr "Страница записей"

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:470
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:477,
#: pro/fields/class-acf-repeater-table.php:478
#, fuzzy
#| msgid "Front Page"
msgid "Next Page"
msgstr "Главная страница"

#: pro/fields/class-acf-repeater-table.php:481,
#: pro/fields/class-acf-repeater-table.php:482
#, fuzzy
#| msgid "Posts Page"
msgid "Last Page"
msgstr "Страница записей"

#: pro/locations/class-acf-location-block.php:71
#, fuzzy
#| msgid "No options pages exist"
msgid "No block types exist"
msgstr "Страницы с настройками отсуствуют"

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "Страницы с настройками отсуствуют"

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "Деактивировать лицензию"

#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "Активировать лицензию"

#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr "Информация о лицензии"

#: pro/admin/views/html-settings-updates.php:34
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Для разблокирования обновлений введите лицензионный ключ ниже. Если у вас "
"его нет, то ознакомьтесь с <a href=\"%s\" target=\"_blank\">деталями</a>."

#: pro/admin/views/html-settings-updates.php:37
msgid "License Key"
msgstr "Номер лицензии"

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr ""

#: pro/admin/views/html-settings-updates.php:29
#, fuzzy
#| msgid "Activation Code"
msgid "Retry Activation"
msgstr "Код активации"

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Обновления"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Текущая версия"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Последняя версия"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Обновления доступны"

#: pro/admin/views/html-settings-updates.php:98
msgid "Upgrade Notice"
msgstr "Замечания по обновлению"

#: pro/admin/views/html-settings-updates.php:126
msgid "Check For Updates"
msgstr ""

#: pro/admin/views/html-settings-updates.php:121
#, fuzzy
#| msgid "Please enter your license key above to unlock updates"
msgid "Enter your license key to unlock updates"
msgstr "Пожалуйста введите ваш номер лицензии для разблокировки обновлений"

#: pro/admin/views/html-settings-updates.php:119
msgid "Update Plugin"
msgstr "Обновить плагин"

#: pro/admin/views/html-settings-updates.php:117
#, fuzzy
#| msgid "Please enter your license key above to unlock updates"
msgid "Please reactivate your license to unlock updates"
msgstr "Пожалуйста введите ваш номер лицензии для разблокировки обновлений"
