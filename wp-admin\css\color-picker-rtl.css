/*! This file is auto-generated */
/* rtl:ignore */
.wp-color-picker {
	width: 80px;
	direction: ltr;
}

.wp-picker-container .hidden {
	display: none;
}

/* Needs higher specificiity. */
.wp-picker-container .wp-color-result.button {
	min-height: 30px;
	margin: 0 0 6px 6px;
	padding: 0 30px 0 0;
	font-size: 11px;
}

.wp-color-result-text {
	background: #f6f7f7;
	border-radius: 2px 0 0 2px;
	border-right: 1px solid #c3c4c7;
	color: #50575e;
	display: block;
	line-height: 2.54545455; /* 28px */
	padding: 0 6px;
	text-align: center;
}

.wp-color-result:hover,
.wp-color-result:focus {
	background: #f6f7f7;
	border-color: #8c8f94;
	color: #1d2327;
}

.wp-color-result:hover:after,
.wp-color-result:focus:after {
	color: #1d2327;
	border-color: #a7aaad;
	border-right: 1px solid #8c8f94;
}

.wp-picker-container {
	display: inline-block;
}

.wp-color-result:focus {
	border-color: #4f94d4;
	box-shadow: 0 0 3px rgba(34, 113, 177, 0.8);
}

.wp-color-result:active {
	/* See Trac ticket #39662 */
	transform: none !important;
}

.wp-picker-open + .wp-picker-input-wrap {
	display: inline-block;
	vertical-align: top;
}

.wp-picker-input-wrap label {
	display: inline-block;
	vertical-align: top;
}

/* For the old `custom-background` page, to override the inline-block and margins from `.form-table td fieldset label`. */
.form-table .wp-picker-input-wrap label {
	margin: 0 !important;
}

.wp-picker-input-wrap .button.wp-picker-default,
.wp-picker-input-wrap .button.wp-picker-clear,
.wp-customizer .wp-picker-input-wrap .button.wp-picker-default,
.wp-customizer .wp-picker-input-wrap .button.wp-picker-clear {
	margin-right: 6px;
	padding: 0 8px;
	line-height: 2.54545455; /* 28px */
	min-height: 30px;
}

.wp-picker-container .iris-square-slider .ui-slider-handle:focus {
	background-color: #50575e
}

.wp-picker-container .iris-picker {
	border-radius: 0;
	border-color: #dcdcde;
	margin-top: 6px;
}

.wp-picker-container input[type="text"].wp-color-picker {
	width: 4rem;
	font-size: 12px;
	font-family: monospace;
	line-height: 2.33333333; /* 28px */
	margin: 0;
	padding: 0 5px;
	vertical-align: top;
	min-height: 30px;
}

.wp-color-picker::-webkit-input-placeholder {
	color: #646970;
}

.wp-color-picker::-moz-placeholder {
	color: #646970;
	opacity: 1;
}

.wp-color-picker:-ms-input-placeholder {
	color: #646970;
}

.wp-picker-container input[type="text"].iris-error {
	background-color: #fcf0f1;
	border-color: #d63638;
	color: #000;
}

.iris-picker .ui-square-handle:focus,
.iris-picker .iris-strip .ui-slider-handle:focus {
	border-color: #3582c4;
	border-style: solid;
	box-shadow: 0 0 0 1px #3582c4;
	outline: 2px solid transparent;
}

.iris-picker .iris-palette:focus {
	box-shadow: 0 0 0 2px #3582c4;
}

@media screen and (max-width: 782px) {
	.wp-picker-container input[type="text"].wp-color-picker {
		width: 5rem;
		font-size: 16px;
		line-height: 1.875; /* 30px */
		min-height: 32px;
	}

	.wp-customizer .wp-picker-container input[type="text"].wp-color-picker {
		padding: 0 5px;
	}

	.wp-picker-input-wrap .button.wp-picker-default,
	.wp-picker-input-wrap .button.wp-picker-clear {
		padding: 0 8px;
		line-height: 2.14285714; /* 30px */
		min-height: 32px;
	}

	.wp-customizer .wp-picker-input-wrap .button.wp-picker-default,
	.wp-customizer .wp-picker-input-wrap .button.wp-picker-clear {
		padding: 0 8px;
		font-size: 14px;
		line-height: 2.14285714; /* 30px */
		min-height: 32px;
	}

	.wp-picker-container .wp-color-result.button {
		padding: 0 40px 0 0;
		font-size: 14px;
		line-height: 2.14285714; /* 30px */
	}

	.wp-customizer .wp-picker-container .wp-color-result.button {
		font-size: 14px;
		line-height: 2.14285714; /* 30px */
	}

	.wp-picker-container .wp-color-result-text {
		padding: 0 14px;
		font-size: inherit;
		line-height: inherit;
	}

	.wp-customizer .wp-picker-container .wp-color-result-text {
		padding: 0 10px;
	}
}
