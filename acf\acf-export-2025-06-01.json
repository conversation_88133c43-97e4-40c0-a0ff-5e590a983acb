[{"key": "group_683a77d2d878c", "title": "Home Page", "fields": [{"key": "field_683a79ce314a5", "label": "Banner Section", "name": "banner_section", "aria-label": "", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_683a7a90314a6", "label": "Title", "name": "title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "", "append": ""}, {"key": "field_683a7a9c314a7", "label": "Description", "name": "description", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "rows": 3, "placeholder": "", "new_lines": ""}, {"key": "field_683a7ae8314a8", "label": "<PERSON><PERSON>", "name": "button", "aria-label": "", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "allow_in_bindings": 0}, {"key": "field_683a8cb25e775", "label": "Image", "name": "image", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "allow_in_bindings": 0, "preview_size": "medium"}]}, {"key": "field_683a7e5c314a9", "label": "Why Section", "name": "why_weaveform", "aria-label": "", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_683a7ec9314aa", "label": "Title", "name": "title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "", "append": ""}, {"key": "field_683a7ed3314ab", "label": "Content Details", "name": "content_details", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "table", "pagination": 0, "min": 0, "max": 3, "collapsed": "", "button_label": "Add Row", "rows_per_page": 20, "sub_fields": [{"key": "field_683a89290610b", "label": "Image Section", "name": "image_section", "aria-label": "", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_683a89290610c", "label": "Options Why", "name": "options_why", "aria-label": "", "type": "radio", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"image": "Image", "logo": "Logo"}, "default_value": "", "return_format": "value", "allow_null": 0, "other_choice": 0, "allow_in_bindings": 0, "layout": "vertical", "save_other_choice": 0}, {"key": "field_683a89290610d", "label": "Image", "name": "image_why", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_683a89290610c", "operator": "==", "value": "image"}]], "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "allow_in_bindings": 0, "preview_size": "medium"}, {"key": "field_683a89290610e", "label": "Logo", "name": "logo_why", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_683a89290610c", "operator": "==", "value": "logo"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "allow_in_bindings": 0, "tabs": "all", "toolbar": "full", "media_upload": 1, "delay": 0}], "parent_repeater": "field_683a7ed3314ab"}, {"key": "field_683a7f5e314ad", "label": "Title", "name": "title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_683a7ed3314ab"}, {"key": "field_683a7f6b314ae", "label": "Description", "name": "description", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "rows": 3, "placeholder": "", "new_lines": "", "parent_repeater": "field_683a7ed3314ab"}]}]}, {"key": "field_683a7fb2314af", "label": "Create Web Section", "name": "create_web_section", "aria-label": "", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_683aa6931a356", "label": "Title", "name": "title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "", "append": ""}, {"key": "field_683aa69b1a357", "label": "<PERSON><PERSON>", "name": "button", "aria-label": "", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "allow_in_bindings": 0}]}, {"key": "field_683a8010314b0", "label": "Functionality and Performance Section", "name": "functionality_and_performance_section", "aria-label": "", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_683a803d314b1", "label": "Title", "name": "title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "", "append": ""}, {"key": "field_683a8047314b2", "label": "Content Details", "name": "content_details", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "table", "pagination": 0, "min": 0, "max": 0, "collapsed": "", "button_label": "Add Row", "rows_per_page": 20, "sub_fields": [{"key": "field_683a8072314b4", "label": "Image Section", "name": "image_section", "aria-label": "", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_683a87d305c89", "label": "Options", "name": "options", "aria-label": "", "type": "radio", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"image": "Image", "logo": "Logo"}, "default_value": "", "return_format": "value", "allow_null": 0, "other_choice": 0, "allow_in_bindings": 0, "layout": "vertical", "save_other_choice": 0}, {"key": "field_683a8491ca7ed", "label": "Image", "name": "image", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_683a87d305c89", "operator": "==", "value": "image"}]], "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "allow_in_bindings": 0, "preview_size": "medium"}, {"key": "field_683a8478ca7ec", "label": "Logo", "name": "logo", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_683a87d305c89", "operator": "==", "value": "logo"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "allow_in_bindings": 0, "tabs": "all", "toolbar": "full", "media_upload": 1, "delay": 0}], "parent_repeater": "field_683a8047314b2"}, {"key": "field_683a8097314b5", "label": "Title", "name": "title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_683a8047314b2"}]}, {"key": "field_683a80af314b6", "label": "<PERSON><PERSON>", "name": "button", "aria-label": "", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "allow_in_bindings": 0}]}], "location": [[{"param": "page_template", "operator": "==", "value": "page-home.php"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0}, {"key": "group_682b85f30f1e5", "title": "Site Configs fields", "fields": [{"key": "field_682b85f33d9f4", "label": "Logo Icon", "name": "logo_icon", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "url", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "allow_in_bindings": 0, "preview_size": "medium"}, {"key": "field_682b86313d9f5", "label": "Logo Text", "name": "logo_text", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "url", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "allow_in_bindings": 0, "preview_size": "medium"}, {"key": "field_682b8bd55a7b9", "label": "Favicon", "name": "favicon", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "url", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "allow_in_bindings": 0, "preview_size": "medium"}, {"key": "field_682b89b99188e", "label": "Social Icons", "name": "social_icons", "aria-label": "", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "row", "sub_fields": [{"key": "field_682b89d39188f", "label": "Youtube URL", "name": "youtube_url", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "", "append": ""}, {"key": "field_682b89db91890", "label": "Facebook URL", "name": "facebook_url", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "", "append": ""}, {"key": "field_682b89e991891", "label": "Twitter URL", "name": "twitter_url", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "", "append": ""}, {"key": "field_682b8a0291892", "label": "Instagram URL", "name": "instagram_url", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "", "append": ""}, {"key": "field_682b8a0891893", "label": "Linkedin URL", "name": "linkedin_url", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "", "append": ""}]}, {"key": "field_682b8adb2a639", "label": "Footer Section", "name": "footer_section", "aria-label": "", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_682b864ab1686", "label": "Logo Icon Footer", "name": "logo_icon_footer", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "url", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "allow_in_bindings": 0, "preview_size": "medium"}, {"key": "field_682b8b01234a9", "label": "Copyright Text", "name": "copyright_text", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "", "append": ""}]}], "location": [[{"param": "options_page", "operator": "==", "value": "site-configs"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0}, {"key": "ui_options_page_682b85d389187", "title": "Site Configs", "active": true, "menu_order": 0, "page_title": "Site Configs", "menu_slug": "site-configs", "parent_slug": "none", "advanced_configuration": 1, "icon_url": "", "menu_title": "", "position": "", "redirect": 0, "description": "", "menu_icon": {"type": "dashicons", "value": "dashicons-admin-generic"}, "update_button": "Update", "updated_message": "Options Updated", "capability": "edit_posts", "data_storage": "options", "post_id": "", "autoload": 0}]